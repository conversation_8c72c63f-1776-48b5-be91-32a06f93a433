import { defineConfig, globalIgnores } from 'eslint/config'
import globals from 'globals'
import js from '@eslint/js'
import pluginVue from 'eslint-plugin-vue'
import { readFileSync } from 'fs'

const autoImportGlobals = JSON.parse(readFileSync('./.eslintrc-auto-import.json', 'utf8'))

export default defineConfig([

  {
    name: 'app/files-to-lint',
    files: ['**/*.{js,mjs,jsx,vue}'],
  },

  globalIgnores(['**/dist/**', '**/dist-ssr/**', '**/coverage/**']),

  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...autoImportGlobals.globals
      },
    },
  },

  js.configs.recommended,
  ...pluginVue.configs['flat/essential'],
])
