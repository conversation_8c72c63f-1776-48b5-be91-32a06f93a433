<!--
/**
 * 商品配送信息展示组件
 *
 * 主要功能：
 * 1. 显示商品配送地址信息，支持点击选择配送地址
 * 2. 展示物流配送时效信息，根据商品类型显示不同的配送徽章
 * 3. 显示退货政策信息，如"7天无理由退货"等
 * 4. 展示售后服务信息，标明服务提供方
 * 5. 根据业务代码(bizCode)控制不同业务场景下的显示内容
 * 6. 支持京东商品的特殊物流显示逻辑，包含京东E和三通一达徽章
 *
 * 技术特点：
 * - 响应式设计，适配不同屏幕尺寸
 * - 支持HTML内容渲染，用于显示富文本物流信息
 * - 条件渲染，根据业务类型和商品属性动态显示内容
 * - 事件向上传递，实现父子组件通信
 *
 * 使用场景：
 * - 商品详情页配送信息展示区域
 * - 需要显示物流、退货、售后等服务信息的商品页面
 */
-->

<template>
  <!-- 配送信息主容器 -->
  <section class="delivery-section">
    <!-- 配送信息包装器 -->
    <div class="delivery-wrapper">
      <!-- 主要配送信息区域，包含地址和物流时效 -->
      <!-- 点击整个区域可以选择配送地址 -->
      <div class="delivery-main" @click="handleAddressClick">
        <!-- 物流图标 -->
        <img src="./assets/logistics.png" alt="物流" class="delivery-icon" />
        <!-- 配送内容区域 -->
        <div class="delivery-content">
          <!-- 配送地址行，显示当前选择的配送地址 -->
          <div class="delivery-location">
            <!-- 配送地址文本，从deliveryInfo.location获取 -->
            <span class="delivery-text">{{ deliveryInfo.location }}</span>
            <!-- 右箭头图标，表示可点击进入地址选择 -->
            <img src="../../static/images/arrow-right-gray.png" alt="箭头" class="arrow-icon" v-if="!showLogisticsServices"/>
          </div>
          <!-- 配送时效信息，仅在显示物流服务且非zq业务时展示 -->
          <div v-if="showLogisticsServices && bizCode !== 'zq'" class="delivery-time">
            <!-- 京东商品且有预测内容时的特殊显示逻辑 -->
            <template v-if="isJD && logisticsInfo.predictContent">
              <!-- 根据物流类型显示不同的配送徽章 -->
              <!-- logisticsType === 1 显示京东E徽章 -->
              <img v-if="logisticsInfo.logisticsType === 1" src="./assets/jdE.png" alt="京东E" class="delivery-badge" />
              <!-- logisticsType === 0或2 显示三通一达徽章 -->
              <img v-else-if="logisticsInfo.logisticsType === 0 || logisticsInfo.logisticsType === 2"
                src="./assets/threeE.png" alt="三通一达" class="delivery-badge" />
              <!-- 显示预测配送内容，支持HTML格式 -->
              <span class="delivery-text" v-html="logisticsInfo.predictContent"></span>
            </template>
            <!-- 非京东商品或无预测内容时的默认显示 -->
            <template v-else>
              <!-- 默认显示三通一达徽章 -->
              <img src="./assets/threeE.png" alt="三通一达" class="delivery-badge" />
              <!-- 默认配送时效文案 -->
              <span class="delivery-text">预计48小时之内发货</span>
            </template>
            <!-- 右箭头图标，表示可点击查看更多物流信息 -->
            <img src="../../static/images/arrow-right-gray.png" alt="箭头" class="arrow-icon" />
          </div>
        </div>
      </div>
      <!-- 退货政策信息，仅在非zq业务时显示 -->
      <div v-if="bizCode !== 'zq'" class="delivery-return">
        <!-- 7天退货图标 -->
        <img src="./assets/7days.png" alt="7天退货" class="return-icon" />
        <!-- 退货政策文本，从deliveryInfo.returnPolicy获取 -->
        <span class="delivery-text">{{ deliveryInfo.returnPolicy }}</span>
      </div>
      <!-- 售后服务信息，仅在非zq业务时显示 -->
      <div v-if="bizCode !== 'zq'" class="delivery-service">
        <!-- 保障服务图标 -->
        <img src="./assets/guarantee.png" alt="保障" class="service-icon" />
        <!-- 售后服务说明文本，包含高亮的服务提供方名称 -->
        <span class="delivery-text">店铺售后由<span class="text-highlight">沃百富商城</span>提供服务</span>
      </div>
    </div>
  </section>
</template>

<script setup>
import { toRefs } from 'vue'
import { getBizCode } from '@/utils/curEnv'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 配送信息对象，包含地址、退货政策等基本配送信息
  deliveryInfo: {
    type: Object,
    required: true,
    default: () => ({
      location: '配送地址',
      predictContent: '预计48小时之内发货',
      returnPolicy: '7天无理由退货',
      service: '店铺售后由沃百富商城提供服务'
    })
  },
  // 物流信息对象，包含物流类型、预测内容等详细物流数据
  logisticsInfo: {
    type: Object,
    default: () => ({
      logisticsType: 1,
      returnRuleStr: '',
      predictContent: '预计48小时之内发货',
      isJD: false
    })
  },
  // 是否为京东商品，用于控制特殊的京东物流显示逻辑
  isJD: {
    type: Boolean,
    default: false
  },
  // 是否显示物流服务信息，控制配送时效等信息的显示
  showLogisticsServices: {
    type: Boolean,
    default: true
  }
})

// 使用toRefs解构props，保持响应性
// 解构后可以直接使用各个属性，同时保持响应式特性
const { deliveryInfo, logisticsInfo, isJD, showLogisticsServices } = toRefs(props)

// 定义组件向父组件发射的事件
// address-click: 用户点击配送地址区域时触发
const emit = defineEmits(['address-click'])

// ===================== 业务逻辑配置 ======================
// 获取当前业务代码，用于区分不同业务场景
// zq业务不显示退货政策和售后服务信息
const bizCode = getBizCode()

// ===================== 用户操作处理 ======================
// 处理配送地址点击事件
// 当用户点击配送地址区域时触发，用于打开地址选择页面
const handleAddressClick = () => {
  // 向父组件发射address-click事件，由父组件处理具体的地址选择逻辑
  emit('address-click')
}
</script>

<style scoped lang="less">
.delivery-section {
  background-color: #FFFFFF;
  padding: 5px 17px;
  box-sizing: border-box;
}

.arrow-icon {
      width: 5px;
      height: 9px;
      margin-left: 4px;
      flex-shrink: 0;
    }

.delivery-wrapper {
  .delivery-main {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;

    .delivery-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      margin-top: 2px;
    }

    .delivery-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;
      overflow: hidden;
    }
  }

  .delivery-location,
  .delivery-time,
  .delivery-return,
  .delivery-service {
    display: flex;
    align-items: center;
    font-size: 13px;
  }

  .delivery-location {
    display: flex;
    justify-content: space-between;
    .delivery-text {
      font-size: 13px;
      color: #171E24;
      font-weight: 500;
    }
  }

  .delivery-time {
    .delivery-badge {
      width: 64px;
      height: 20px;
      margin-right: 8px;
    }

    .delivery-text {
      color: #4A5568;
      flex: 1;
      overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
    }
  }



  .delivery-return {
    margin-bottom: 12px;

    .return-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .delivery-text {
      color: #4A5568;
    }
  }

  .delivery-service {
    .service-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .delivery-text {
      color: #4A5568;

      .text-highlight {
        color: var(--wo-biz-theme-color);
      }
    }
  }
}
</style>
