import { createAlova } from 'alova';
import { axiosRequestAdapter } from '@alova/adapter-axios';
import VueHook from 'alova/vue';
import { onSuccess, onError } from './responseHandlers';

/**
 * alova 实例（全局复用）
 *
 * - statesHook: 指定状态管理为 VueHook
 * - timeout: 全局请求超时时间（毫秒）
 * - requestAdapter: 使用 axios 作为底层请求适配器
 * - responded: 响应拦截器，统一成功/失败处理
 *
 * 注意：重试不在此处设置，见 src/api/config/index.js 中的 sendWithRetry
 * @type {import('alova').Alova}
 */
/**
 * 创建 alova 实例
 * 说明：alova v3 的重试属于 Hook/中间件层（如 useRetriableRequest），
 * 直接在 createAlova 中配置 retry 不会生效。
 * 本项目改为在 src/api/config/index.js 的 sendWithRetry 里统一实现重试。
 */
const alovaInstance = createAlova({
  // 指定使用的状态管理钩子，这里使用 Vue 的响应式系统
  statesHook: VueHook,
  // 全局请求超时设置（单位：毫秒）
  timeout: 30000,
  // 使用的请求适配器，这里配置为 axios
  requestAdapter: axiosRequestAdapter(),
  // 响应拦截器配置
  responded: {
    onSuccess,
    onError
  },
});

// 为 alova 实例的方法打补丁：自动注入 AbortSignal 并在完成后回收
import { buildCancellation, finalizeCancellation } from './cancellation.js';

const wrapMethodFactory = (originFactory) => {
  return function wrappedFactory(url, ...args) {
    // 创建原始 Method 实例（暂不发送）
    const method = originFactory.call(alovaInstance, url, ...args);
    // 记录原始 send
    const originalSend = method.send.bind(method);

    // 包装 send：在 send 时根据 method.config 注入 signal，并做回收
    method.send = async (...sendArgs) => {
      // method.config 可被使用方传入 cancelKey/cancelScope/cancelPage/autoTrack/signal
      const { signal, controller, rest, meta } = buildCancellation(method.config || {});
      // 若有 rest，合并回 method.config（不改变既有字段）
      if (rest && rest !== method.config) {
        method.config = { ...method.config, ...rest };
      }
      if (signal && !method.config.signal) {
        method.config = { ...method.config, signal };
      }
      try {
        return await originalSend(...sendArgs);
      } finally {
        finalizeCancellation(controller, meta);
      }
    };

    return method;
  };
};

// 对 Get/Post/Put/Delete 等常用工厂进行包装
alovaInstance.Get = wrapMethodFactory(alovaInstance.Get);
alovaInstance.Post = wrapMethodFactory(alovaInstance.Post);
if (alovaInstance.Put) alovaInstance.Put = wrapMethodFactory(alovaInstance.Put);
if (alovaInstance.Delete) alovaInstance.Delete = wrapMethodFactory(alovaInstance.Delete);


export default alovaInstance;
