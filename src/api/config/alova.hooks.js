// 单独导出 alova v3 的 Hook 与串行/委托等能力，集中入口
// 使用：
// import { useRequest, useWatcher, useFetcher } from '@/api/config/hooks'
// import { actionDelegationMiddleware, useSerialRequest, useSerialWatcher } from '@/api/config/hooks'

export { useRequest, useWatcher, useFetcher } from 'alova/client';
export { actionDelegationMiddleware } from 'alova/client';
export { useSerialRequest, useSerialWatcher } from 'alova/client';

