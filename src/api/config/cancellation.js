// 请求取消与作用域管理（适配 alova v3 + axios adapter）
// 设计目标：
// - 与现有 Promise 风格接口无缝结合（无需在每个调用点传参）
// - 支持单个 / 多个 / 页面 / 自定义作用域取消
// - 提供“上下文作用域”（页面/自定义）机制：在上下文激活期间发起的请求自动被追踪

import { onBeforeUnmount } from 'vue';
import { useRoute, onBeforeRouteLeave } from 'vue-router';

// 内部存储
const allControllers = new Set();
const keyMap = new Map();    // key -> Set<AbortController>
const scopeMap = new Map();  // scope -> Set<AbortController>
const pageMap = new Map();   // pageId -> Set<AbortController>

// 作用域上下文栈：后进先出，顶层上下文为当前生效
const contextStack = [];
export const getActiveContext = () => contextStack[contextStack.length - 1] || null;
export const pushContext = (ctx) => { if (ctx) contextStack.push(ctx); };
export const popContext = () => { contextStack.pop(); };

const addToMapSet = (map, k, v) => {
  if (!k) return;
  let set = map.get(k);
  if (!set) {
    set = new Set();
    map.set(k, set);
  }
  set.add(v);
};

const removeFromMapSet = (map, k, v) => {
  if (!k) return;
  const set = map.get(k);
  if (!set) return;
  set.delete(v);
  if (set.size === 0) map.delete(k);
};

export const registerController = (controller, meta = {}) => {
  const { cancelKey, cancelScope, cancelPage } = meta;
  allControllers.add(controller);
  addToMapSet(keyMap, cancelKey, controller);
  addToMapSet(scopeMap, cancelScope, controller);
  addToMapSet(pageMap, cancelPage, controller);
};

export const unregisterController = (controller, meta = {}) => {
  const { cancelKey, cancelScope, cancelPage } = meta;
  allControllers.delete(controller);
  removeFromMapSet(keyMap, cancelKey, controller);
  removeFromMapSet(scopeMap, cancelScope, controller);
  removeFromMapSet(pageMap, cancelPage, controller);
};

const abortSet = (set) => {
  if (!set) return;
  for (const ctl of Array.from(set)) {
    try { ctl.abort(); } catch {}
  }
};

export const cancelByKey = (key) => abortSet(keyMap.get(key));
export const cancelByKeys = (keys = []) => keys.forEach(k => cancelByKey(k));
export const cancelByScope = (scope) => abortSet(scopeMap.get(scope));
export const cancelByScopes = (scopes = []) => scopes.forEach(s => cancelByScope(s));
export const cancelByPage = (pageId) => abortSet(pageMap.get(pageId));
export const cancelAll = () => abortSet(allControllers);

// 生成/提取取消元信息与 signal；若未开启追踪则返回空
// 入参 config 可选包含：cancelKey、cancelScope、cancelPage、autoTrack、signal
export const buildCancellation = (config = {}) => {
  const ctx = getActiveContext();
  const {
    cancelKey = undefined,
    cancelScope = ctx?.cancelScope,
    cancelPage = ctx?.cancelPage,
    autoTrack = ctx?.autoTrack ?? true,
    signal: externalSignal,
    ...rest
  } = config || {};

  const meta = { cancelKey, cancelScope, cancelPage };

  if (externalSignal) {
    return { signal: externalSignal, controller: null, rest, meta };
  }

  if (autoTrack === false || (!cancelKey && !cancelScope && !cancelPage)) {
    return { signal: undefined, controller: null, rest, meta };
  }

  const controller = new AbortController();
  registerController(controller, meta);
  return { signal: controller.signal, controller, rest, meta };
};

export const finalizeCancellation = (controller, meta) => {
  if (!controller) return;
  unregisterController(controller, meta || {});
};

// 组合式：在组件卸载或路由离开时自动取消某个作用域
export const useCancelScope = (scope) => {
  const cancel = () => cancelByScope(scope);
  onBeforeUnmount(cancel);
  return { cancel };
};

export const usePageCancellation = (pageId) => {
  const route = useRoute();
  const pid = pageId || route.fullPath;
  const cancel = () => cancelByPage(pid);
  onBeforeUnmount(cancel);
  onBeforeRouteLeave(() => { cancel(); });
  return { cancel, pageId: pid };
};


// 组合式：开启一个 Alova 上下文作用域，期间发起的请求会自动带上 cancelScope/cancelPage
export const useAlovaContext = (options = {}) => {
  const { scope, pageId, autoTrack = true } = options || {};
  const ctx = { cancelScope: scope, cancelPage: pageId, autoTrack };
  pushContext(ctx);
  onBeforeUnmount(() => popContext());
  return { end: () => popContext() };
};

