// 按照 alova 规范重新封装 API 方法
import alovaInstance from "./alova.config.js";
import { getJsonPostConfig, getFormGetConfig, getFormPostConfig } from "./requestConfig.js";
import { stripRetryKeys } from './utils.js';
import { clamp } from 'es-toolkit';
import { cancelByKey, cancelByKeys, cancelByScope, cancelByScopes, cancelByPage, cancelAll, useCancelScope } from './cancellation.js';


// 统一的可配置重试发送器（指数退避 + 抖动）
/**
 * 通用重试发送器（指数退避 + 抖动）
 * 注意：不修改 method/adapter 逻辑，仅在失败时按策略重试 method.send()
 *
 * @template T
 * @param {import('alova').Method<T>} method - alova 的 Method 实例（内部已配置拦截器）
 * @param {Object} [options] - 重试相关配置
 * @param {number|false} [options.retry=3] - 最大重试次数；传 false/0 关闭重试
 * @param {number} [options.retryDelay=500] - 初始等待时长（毫秒）
 * @param {number} [options.multiplier=2] - 每次重试的指数退避乘数
 * @param {number} [options.maxDelay=8000] - 单次等待的最大时长（毫秒）
 * @param {number} [options.jitter=0.2] - 抖动幅度（0~1），用于分散同一时间的重试风暴
 * @param {(err:any)=>boolean} [options.shouldRetry] - 是否应该进行重试的判定函数
 * @returns {Promise<any>} 成功时返回 onSuccess 处理后的数据，失败时抛出最后一次错误
 */
const sendWithRetry = async (method, options = {}) => {
  const {
    // 开关：传入 false 关闭重试
    retry = 3,
    // 退避参数
    retryDelay = 500,
    multiplier = 2,
    maxDelay = 8000,
    // 抖动范围（0~1），0 表示无抖动
    jitter = 0.2,
    // 重试条件（返回 true 才会重试）
    shouldRetry = (err) => {
      // 取消的请求不应重试，主动取消的不重试
      // const code = err?.code;
      // const name = err?.name;
      // const msg = String(err?.message || '').toLowerCase();
      // const aborted = err?.config?.signal?.aborted === true || err?.request?.aborted === true;
      // if (code === 'ERR_CANCELED' || name === 'AbortError' || msg.includes('canceled') || msg.includes('cancelled') || aborted) {
      //   return false;
      // }
      const status = err?.response?.status;
      // 无响应（网络、超时）或 5xx/408/429
      return !status || status >= 500 || status === 408 || status === 429;
    }
  } = options;

  let attempt = 0;
  // 注意：method.send() 会根据 onError 抛出 error
  while (true) {
    try {
      return await method.send();
    } catch (err) {
      // 不满足重试条件或达到次数
      if (!retry || attempt >= (typeof retry === 'number' ? retry : 0) || !shouldRetry(err)) {
        throw err;
      }
      attempt += 1;
      // 计算指数退避延时（使用 clamp 限制 base 范围）
      const base = clamp(retryDelay * Math.pow(multiplier, attempt - 1), 0, maxDelay);
      const rand = 1 + (Math.random() * 2 - 1) * jitter; // [1-jitter, 1+jitter]
      const delay = Math.max(0, Math.floor(base * rand));
      await new Promise(r => setTimeout(r, delay));
    }
  }
};



/**
 * JSON 格式 POST 请求
 */
/**
 * JSON 格式 POST 请求（Content-Type: application/json）
 * 仅封装 Method 构建与重试策略透传，不改变业务字段
 *
 * @param {string} url - 请求地址
 * @param {Object} [data={}] - 请求体（会直接作为 body 传入）
 * @param {Record<string,string>} [headers={}] - 额外请求头
 * @param {Object} [config={}] - 其他 alova Method 配置项及可选的重试项
 * @returns {Promise<any>} 返回接口响应的业务数据
 */
export const jsonPost = async (url, data = {}, headers = {}, config = {}) => {
  const requestConfig = getJsonPostConfig(data, headers, config);
  const { rest, retryOptions } = stripRetryKeys(config);
  const method = alovaInstance.Post(url, requestConfig.body, {
    headers: requestConfig.headers,
    ...rest
  });
  return sendWithRetry(method, retryOptions);
};

/**
 * 表单 GET 请求
 */
/**
 * 表单 GET 请求（通过 params 传参）
 *
 * @param {string} url - 请求地址
 * @param {Object} [data={}] - 查询参数
 * @param {Record<string,string>} [headers={}] - 额外请求头
 * @param {Object} [config={}] - 其他 alova Method 配置项及可选的重试项
 * @returns {Promise<any>} 返回接口响应的业务数据
 */
export const formGet = async (url, data = {}, headers = {}, config = {}) => {
  const requestConfig = getFormGetConfig(data, headers, config);
  const { rest, retryOptions } = stripRetryKeys(config);
  const method = alovaInstance.Get(url, {
    params: requestConfig.params,
    headers: requestConfig.headers,
    ...rest
  });
  return sendWithRetry(method, retryOptions);
};

/**
 * 表单 POST 请求
 */
/**
 * 表单 POST 请求（Content-Type: application/x-www-form-urlencoded）
 *
 * @param {string} url - 请求地址
 * @param {Object} [data={}] - 将被序列化为表单字符串的对象
 * @param {Record<string,string>} [headers={}] - 额外请求头
 * @param {Object} [config={}] - 其他 alova Method 配置项及可选的重试项
 * @returns {Promise<any>} 返回接口响应的业务数据
 */
export const formPost = async (url, data = {}, headers = {}, config = {}) => {
  const requestConfig = getFormPostConfig(data, headers, config);
  const { rest, retryOptions } = stripRetryKeys(config);
  const method = alovaInstance.Post(url, requestConfig.body, {
    headers: requestConfig.headers,
    ...rest
  });
  return sendWithRetry(method, retryOptions);
};


// 导出取消请求相关 API，供业务侧使用
export { cancelByKey, cancelByKeys, cancelByScope, cancelByScopes, cancelByPage, cancelAll } from './cancellation.js';
export { useCancelScope } from './cancellation.js';
