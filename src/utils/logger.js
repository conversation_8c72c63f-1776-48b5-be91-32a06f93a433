/**
 * 全局Logger系统
 * 支持不同级别的日志、颜色区分、console劫持等功能
 */

// 日志级别定义
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  LOG: 2,
  WARN: 3,
  ERROR: 4
}

// 日志级别颜色配置
const LOG_COLORS = {
  DEBUG: '#6B7280', // 灰色
  INFO: '#3B82F6',  // 蓝色
  LOG: '#10B981',   // 绿色
  WARN: '#F59E0B',  // 橙色
  ERROR: '#EF4444'  // 红色
}

// 日志级别背景色配置
const LOG_BG_COLORS = {
  DEBUG: '#F3F4F6',
  INFO: '#EBF8FF',
  LOG: '#ECFDF5',
  WARN: '#FFFBEB',
  ERROR: '#FEF2F2'
}

/**
 * 应用日志管理工具类
 * 提供统一的日志输出接口，支持不同级别的日志记录
 * 在开发环境下输出日志，生产环境下静默处理
 */
class Logger {
  /**
   * 创建日志记录器实例
   * 
   * @param {Object} options 配置选项
   * @param {string} options.appName 应用名称标识，用于区分不同应用的日志，默认为 'APP'
   * @param {number} options.level 日志级别，控制输出的最低日志级别
   * @param {boolean} options.enableConsoleHijack 是否启用控制台劫持功能
   * @param {boolean} options.enableTimestamp 是否在日志中显示时间戳
   * @param {number} options.maxLogs 最大日志存储数量，超出时自动清理旧日志
   * 
   * @example
   * const logger = new Logger({
   *   appName: 'USER_MODULE',
   *   level: LOG_LEVELS.INFO,
   *   enableConsoleHijack: true
   * })
   */
  constructor(options = {}) {
    // 设置应用名称标识
    this.appName = options.appName || 'APP'
    // 设置日志输出级别
    this.level = options.level || LOG_LEVELS.DEBUG
    // 是否启用控制台方法劫持
    this.enableConsoleHijack = options.enableConsoleHijack !== false
    // 是否启用时间戳显示
    this.enableTimestamp = options.enableTimestamp !== false
    // 设置最大日志存储数量
    this.maxLogs = options.maxLogs || 1000

    // 存储所有日志记录的数组
    this.logs = []

    // 备份原始的控制台方法，避免劫持后无法使用原始功能
    this.originalConsole = {
      log: console.log,
      info: console.info,
      warn: console.warn,
      error: console.error,
      debug: console.debug
    }

    // 执行初始化设置
    this.init()
  }

  /**
   * 初始化Logger
   */
  init() {
    if (this.enableConsoleHijack) {
      this.hijackConsole()
    }

    // 监听未捕获的错误
    this.setupErrorHandlers()
  }

  /**
   * 劫持全局console
   */
  hijackConsole() {
    const self = this

    console.log = (...args) => {
      // 使用Logger的log方法，包含格式化和样式输出
      self.log('LOG', ...args)
    }

    console.info = (...args) => {
      self.log('INFO', ...args)
    }

    console.warn = (...args) => {
      self.log('WARN', ...args)
    }

    console.error = (...args) => {
      // 使用增强的error方法
      self.error(...args)
    }

    console.debug = (...args) => {
      self.log('DEBUG', ...args)
    }
  }

  /**
   * 设置错误处理器
   */
  setupErrorHandlers() {
    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      const errorInfo = this.parseErrorDetails(event.reason)
      this.error('未处理的Promise拒绝:', {
        reason: event.reason,
        ...errorInfo
      })
    })

    // 捕获全局错误
    window.addEventListener('error', (event) => {
      const errorInfo = this.parseErrorDetails(event.error, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        message: event.message
      })
      this.error('全局错误:', {
        message: event.message,
        ...errorInfo
      })
    })

    // 捕获资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.error('资源加载错误:', {
          type: '资源加载失败',
          element: event.target.tagName,
          source: event.target.src || event.target.href,
          message: `Failed to load ${event.target.tagName}: ${event.target.src || event.target.href}`
        })
      }
    }, true)

    // 捕获Vue错误（如果在Vue环境中）
    if (typeof window !== 'undefined' && window.Vue) {
      const originalErrorHandler = window.Vue.config.errorHandler
      window.Vue.config.errorHandler = (err, vm, info) => {
        const errorInfo = this.parseErrorDetails(err)
        this.error('Vue错误:', {
          error: err,
          component: vm?.$options.name || vm?.$options._componentTag || 'Unknown',
          info,
          ...errorInfo
        })
        if (originalErrorHandler) {
          originalErrorHandler(err, vm, info)
        }
      }
    }
  }

  /**
   * 解析错误详细信息
   */
  parseErrorDetails(error, fallbackInfo = {}) {
    const details = {
      filename: fallbackInfo.filename || 'unknown',
      lineno: fallbackInfo.lineno || 0,
      colno: fallbackInfo.colno || 0,
      functionName: 'unknown',
      stack: null,
      errorType: 'Error',
      severity: 'high'
    }

    if (!error) {
      return details
    }

    // 如果是Error对象
    if (error instanceof Error) {
      details.errorType = error.constructor.name
      details.stack = error.stack
      
      // 解析堆栈信息
      if (error.stack) {
        const stackInfo = this.parseStackTrace(error.stack)
        if (stackInfo.length > 0) {
          const topFrame = stackInfo[0]
          details.filename = topFrame.filename || details.filename
          details.lineno = topFrame.lineno || details.lineno
          details.colno = topFrame.colno || details.colno
          details.functionName = topFrame.functionName || details.functionName
        }
      }
    }

    // 判断错误严重级别
    details.severity = this.getErrorSeverity(error, details)

    return details
  }

  /**
   * 解析堆栈跟踪
   */
  parseStackTrace(stack) {
    const frames = []
    if (!stack) return frames

    const lines = stack.split('\n')
    for (const line of lines) {
      const frame = this.parseStackFrame(line)
      if (frame) {
        frames.push(frame)
      }
    }

    return frames
  }

  /**
   * 解析单个堆栈帧
   */
  parseStackFrame(line) {
    if (!line || line.trim() === '') return null

    // 匹配不同浏览器的堆栈格式
    const patterns = [
      // Chrome/Edge: "    at functionName (filename:line:col)"
      /^\s*at\s+([^\s]+)\s+\((.+?):(\d+):(\d+)\)$/,
      // Chrome/Edge: "    at filename:line:col"
      /^\s*at\s+(.+?):(\d+):(\d+)$/,
      // Firefox: "functionName@filename:line:col"
      /^([^@]+)@(.+?):(\d+):(\d+)$/,
      // Safari: "functionName@filename:line:col"
      /^([^@]*)@(.+?):(\d+):(\d+)$/
    ]

    for (const pattern of patterns) {
      const match = line.match(pattern)
      if (match) {
        let functionName, filename, lineno, colno
        
        if (pattern === patterns[0]) {
          // Chrome with function name
          [, functionName, filename, lineno, colno] = match
        } else if (pattern === patterns[1]) {
          // Chrome without function name
          [, filename, lineno, colno] = match
          functionName = 'anonymous'
        } else {
          // Firefox/Safari
          [, functionName, filename, lineno, colno] = match
        }

        return {
          functionName: functionName || 'anonymous',
          filename: this.cleanFilename(filename),
          lineno: parseInt(lineno, 10),
          colno: parseInt(colno, 10),
          raw: line.trim()
        }
      }
    }

    return null
  }

  /**
   * 清理文件名，移除URL参数和hash
   */
  cleanFilename(filename) {
    if (!filename) return 'unknown'
    
    // 移除URL参数和hash
    const cleanName = filename.split('?')[0].split('#')[0]
    
    // 如果是完整URL，只保留文件名部分
    if (cleanName.includes('/')) {
      const parts = cleanName.split('/')
      return parts[parts.length - 1] || cleanName
    }
    
    return cleanName
  }

  /**
   * 判断错误严重级别
   */
  getErrorSeverity(error, details) {
    if (!error) return 'low'

    // 根据错误类型判断严重级别
    const criticalErrors = [
      'ReferenceError',
      'TypeError',
      'SyntaxError',
      'RangeError'
    ]

    const warningErrors = [
      'NetworkError',
      'TimeoutError',
      'AbortError'
    ]

    if (criticalErrors.includes(details.errorType)) {
      return 'critical'
    }

    if (warningErrors.includes(details.errorType)) {
      return 'medium'
    }

    // 根据错误消息判断
    const errorMessage = error.message || error.toString()
    if (errorMessage.includes('Cannot read') || 
        errorMessage.includes('undefined') || 
        errorMessage.includes('null')) {
      return 'high'
    }

    return 'medium'
  }

  /**
   * 格式化时间戳
   */
  formatTimestamp() {
    const now = new Date()
    return now.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
  }

  /**
   * 格式化日志消息
   */
  formatMessage(level, ...args) {
    const timestamp = this.enableTimestamp ? this.formatTimestamp() : ''
    const prefix = `[${this.appName}]`
    const levelTag = `[${level}]`

    return {
      timestamp,
      prefix,
      level,
      levelTag,
      args,
      fullMessage: `${timestamp} ${prefix} ${levelTag}`,
      color: LOG_COLORS[level],
      bgColor: LOG_BG_COLORS[level]
    }
  }

  /**
   * 核心日志方法
   */
  log(level, ...args) {
    if (LOG_LEVELS[level] < this.level) {
      return
    }

    const logEntry = {
      id: Date.now() + Math.random(),
      timestamp: new Date(),
      level,
      args,
      formatted: this.formatMessage(level, ...args)
    }

    // 添加到日志记录
    this.addLogEntry(logEntry)

    // 控制台输出（带样式）
    this.outputToConsole(logEntry)

    // 触发日志事件
    this.emitLogEvent(logEntry)
  }

  /**
   * 只记录到内存的日志方法（用于console劫持，避免重复输出）
   */
  logToMemoryOnly(level, ...args) {
    if (LOG_LEVELS[level] < this.level) {
      return
    }

    const logEntry = {
      id: Date.now() + Math.random(),
      timestamp: new Date(),
      level,
      args,
      formatted: this.formatMessage(level, ...args)
    }

    // 只添加到日志记录，不输出到控制台
    this.addLogEntry(logEntry)

    // 触发日志事件
    this.emitLogEvent(logEntry)
  }

  /**
   * 添加日志记录
   */
  addLogEntry(logEntry) {
    this.logs.unshift(logEntry)

    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs)
    }
  }

  /**
   * 控制台输出（带样式）
   */
  outputToConsole(logEntry) {
    const { formatted } = logEntry
    const style = `
      color: ${formatted.color};
      background-color: ${formatted.bgColor};
      padding: 2px 6px;
      border-radius: 3px;
      font-weight: bold;
    `

    // 使用原始console方法输出，避免无限递归
    if (logEntry.level === 'ERROR') {
      // 特殊处理ERROR级别，显示详细的错误信息
      this.outputErrorToConsole(logEntry, style)
    } else if (logEntry.level === 'WARN') {
      this.originalConsole.warn(
        `%c${formatted.fullMessage}`,
        style,
        ...logEntry.args
      )
    } else {
      this.originalConsole.log(
        `%c${formatted.fullMessage}`,
        style,
        ...logEntry.args
      )
    }
  }

  /**
   * 输出增强的错误信息到控制台
   */
  outputErrorToConsole(logEntry, style) {
    const { formatted } = logEntry
    
    // 输出基本错误信息
    this.originalConsole.error(
      `%c${formatted.fullMessage}`,
      style,
      ...logEntry.args
    )

    // 查找并输出详细的错误位置信息
    logEntry.args.forEach(arg => {
      if (arg && typeof arg === 'object') {
        if (arg.location && (arg.type === 'ErrorLocation' || arg.severity)) {
          const locationStyle = `
            color: #DC2626;
            background-color: #FEF2F2;
            padding: 1px 4px;
            border-radius: 2px;
            font-size: 12px;
          `
          
          const locationInfo = `📍 ${arg.location.file}:${arg.location.line}:${arg.location.column} in ${arg.location.function}()`
          
          this.originalConsole.error(
            `%c${locationInfo}`,
            locationStyle
          )
          
          // 如果有严重级别，显示严重级别
          if (arg.severity) {
            const severityStyle = this.getSeverityStyle(arg.severity)
            this.originalConsole.error(
              `%c🚨 严重级别: ${arg.severity.toUpperCase()}`,
              severityStyle
            )
          }
        }
      }
    })
  }

  /**
   * 获取严重级别样式
   */
  getSeverityStyle(severity) {
    const styles = {
      critical: 'color: #FFFFFF; background-color: #DC2626; padding: 2px 6px; border-radius: 3px; font-weight: bold;',
      high: 'color: #FFFFFF; background-color: #EA580C; padding: 2px 6px; border-radius: 3px; font-weight: bold;',
      medium: 'color: #FFFFFF; background-color: #D97706; padding: 2px 6px; border-radius: 3px; font-weight: bold;',
      low: 'color: #374151; background-color: #F3F4F6; padding: 2px 6px; border-radius: 3px; font-weight: bold;'
    }
    return styles[severity] || styles.medium
  }

  /**
   * 触发日志事件
   */
  emitLogEvent(logEntry) {
    // 派发自定义事件，供UI组件监听
    window.dispatchEvent(new CustomEvent('logger:newLog', {
      detail: logEntry
    }))
  }

  /**
   * 公共API方法
   */
  debug(...args) {
    this.log('DEBUG', ...args)
  }

  info(...args) {
    this.log('INFO', ...args)
  }

  warn(...args) {
    this.log('WARN', ...args)
  }

  error(...args) {
    // 增强错误日志处理
    const enhancedArgs = this.enhanceErrorArgs(...args)
    this.log('ERROR', ...enhancedArgs)
  }

  /**
   * 增强错误参数，添加详细的错误信息
   */
  enhanceErrorArgs(...args) {
    const enhanced = [...args]
    
    // 查找Error对象
    for (let i = 0; i < args.length; i++) {
      const arg = args[i]
      if (arg instanceof Error) {
        const errorDetails = this.parseErrorDetails(arg)
        
        // 创建增强的错误信息对象
        const enhancedError = {
          message: arg.message,
          type: errorDetails.errorType,
          severity: errorDetails.severity,
          location: {
            file: errorDetails.filename,
            line: errorDetails.lineno,
            column: errorDetails.colno,
            function: errorDetails.functionName
          },
          stack: errorDetails.stack,
          timestamp: new Date().toISOString()
        }
        
        enhanced[i] = enhancedError
        break
      }
    }
    
    // 如果没有Error对象，但有字符串错误信息，尝试获取调用栈
    if (!enhanced.some(arg => arg instanceof Error)) {
      try {
        const stack = new Error().stack
        if (stack) {
          const stackInfo = this.parseStackTrace(stack)
          // 跳过logger内部的调用栈，找到实际调用位置
          const callerFrame = stackInfo.find(frame => 
            !frame.filename.includes('logger.js') && 
            !frame.filename.includes('Logger')
          )
          
          if (callerFrame) {
            enhanced.push({
              type: 'ErrorLocation',
              location: {
                file: callerFrame.filename,
                line: callerFrame.lineno,
                column: callerFrame.colno,
                function: callerFrame.functionName
              },
              message: '错误发生位置'
            })
          }
        }
      } catch (e) {
        // 忽略获取调用栈时的错误
      }
    }
    
    return enhanced
  }

  /**
   * 获取所有日志
   */
  getLogs() {
    return [...this.logs]
  }

  /**
   * 清空日志
   */
  clearLogs() {
    this.logs = []
    window.dispatchEvent(new CustomEvent('logger:cleared'))
  }

  /**
   * 设置日志级别
   */
  setLevel(level) {
    if (typeof level === 'string') {
      this.level = LOG_LEVELS[level.toUpperCase()] || LOG_LEVELS.DEBUG
    } else {
      this.level = level
    }
  }

  /**
   * 恢复原始console
   */
  restoreConsole() {
    Object.assign(console, this.originalConsole)
  }

  /**
   * 销毁Logger
   */
  destroy() {
    this.restoreConsole()
    this.logs = []
  }
}

// 从package.json获取应用名称
let appName = 'APP'
try {
  // 动态获取package.json中的name字段并转为大写
  const getAppName = () => {
    // 尝试从import.meta.env获取
    if (import.meta.env.VITE_APP_NAME) {
      return import.meta.env.VITE_APP_NAME.toUpperCase()
    }
    // 默认使用项目名称
    return 'PS-CCMS-BIZ-WEB'
  }
  appName = getAppName()
} catch (e) {
  console.warn('无法获取应用名称，使用默认值')
}

// 创建全局Logger实例
const globalLogger = new Logger({
  appName,
  level: LOG_LEVELS.DEBUG,
  enableConsoleHijack: true,
  enableTimestamp: true
})

// 创建简洁的全局日志方法
const logger = {
  debug: (...args) => globalLogger.debug(...args),
  info: (...args) => globalLogger.info(...args),
  warn: (...args) => globalLogger.warn(...args),
  error: (...args) => globalLogger.error(...args),
  log: (...args) => globalLogger.log('LOG', ...args),

  // 高级方法
  getLogs: () => globalLogger.getLogs(),
  clearLogs: () => globalLogger.clearLogs(),
  setLevel: (level) => globalLogger.setLevel(level),

  // 原始实例访问（如果需要高级功能）
  _instance: globalLogger
}

// 导出Logger类、实例和简洁方法
export { Logger, LOG_LEVELS, LOG_COLORS, globalLogger }
export default logger
