import { copy } from '@/utils/clipboard'
import { getFuLiHuiID } from '@api/interface/flh.js'
import { getBizCode } from '@/utils/curEnv'
import { curDeveloperId } from '@utils/storage.js'
import { showToast } from 'vant'

/**
 * 获取默认分享URL
 * 根据当前地址拼接 distri_biz_code 等必要参数；福利汇场景附加 developerId
 *
 * @returns {Promise<string>} 分享URL
 *
 * @example
 * const url = await getDefaultShareUrl()
 * console.log(url)
 */
export const getDefaultShareUrl = async () => {
  const urlSearch = window.location.search
  const urlQueryStr = urlSearch ? urlSearch.split('?')[1] : ''

  let url = window.location.origin + window.location.pathname + urlSearch
  if (!(urlQueryStr.indexOf('distri_biz_code') >= 0)) {
    url += url.indexOf('?') >= 0 ? '&' : '?'
    const bizCode = getBizCode()
    if (bizCode === 'fulihui') {
      let developerId = curDeveloperId.get()
      if (!developerId) {
        const [err, json] = await getFuLiHuiID({ bizCode })
        if (!err) {
          developerId = json || ''
        }
      }
      curDeveloperId.set(developerId)

      const shareUrl = url + 'distri_biz_code=' + bizCode + '&developerId=' + developerId
      return shareUrl
    }
    const shareUrl = url + 'distri_biz_code=' + bizCode
    return shareUrl
  }
  return url
}

/**
 * 分享数据配置对象
 * 用于设置分享链接、标题、描述、图片；并在不同环境执行后续处理
 *
 * @type {{
 *   link: string,
 *   title: string,
 *   describe: string,
 *   picUrl: string,
 *   next: (options: {type: string}, userData?: any) => void,
 *   callback: () => void
 * }}
 *
 * @example
 * // 设置分享信息
 * shareData.link = await getDefaultShareUrl()
 * shareData.title = '标题'
 * shareData.describe = '描述'
 * shareData.picUrl = 'https://example.com/logo.png'
 */
export const shareData = {
  link: '',
  title: '',
  describe: '',
  picUrl: '',
  /**
   * 分享后续处理函数
   * @param {Object} options - 分享选项
   * @param {string} options.type - 分享类型，如 'h5', 'weixin'
   * @param {Object} userData - 用户数据
   */
  next: (options, userData) => {
    if (options.type === 'h5') {
      copy(shareData.link)
    } else if (options.type === 'weixin') {
      showToast('请点击屏幕右上方的按钮进行分享')
    }
  },
  /**
   * 分享回调函数
   */
  callback: () => {

  }
}
