import { storage } from 'commonkit'

/**
 * 分销业务码存储
 */
export const curDistriBiz = storage('PS_CCMS_DISTRI_BIZ')

/**
 * 渠道码存储
 */
export const curChannelBiz = storage('PS_CCMS_CHANNEL_BIZ')

/**
 * 发展人ID存储
 */
export const curDeveloperId = storage('PS_CCMS_DEVELOPER_ID')

/**
 * 京东开普勒引导页时间控制存储
 */
export const curJdGuidePageDate = storage('PS_CCMS_JD_GUIDE_PAGE', true)

/**
 * 订单确认页分期编码存储
 */
export const curLoanCode = storage('PS_CCMS_LOAN_CODE')

/**
 * 当前商品池ID存储
 */
export const curClassificationId = storage('PS_CCMS_CLASSIFICATION_ID')

/**
 * 扶贫商城荣誉证书图片base64缓存存储
 */
export const fpCertificateCache = storage('PS_CCMS_FP_CERTIFICATE', true)

/**
 * 首页浮标控制存储
 */
export const indexFloatTag = storage('PS_CCMS_INDEX_FLOAT_TAG')

/**
 * 省分助农地址栏获取分类ID存储
 */
export const categoryPid = storage('PS_CCMS_CATEGORY_PID')

/**
 * 用户缓存的临时地址信息存储
 */
export const curTempAddrInfo = storage('PS_CCMS_TEMP_ADDR_INFO')

/**
 * 当前渠道对应登录类型存储（0-默认模式，1-查询政企数据模式）
 */
export const loginType = storage('PS_CCMS_LOGIN_TYPE')

/**
 * 政企渠道当前登录角色存储
 */
export const zqRole = storage('PS_CCMS_ZQ_ROLE')

/**
 * 立即购买的商品存储
 * @description 使用 localStorage 持久化
 * @example
 * // 设置
 * buyProductNow.set({ skuId: '123' })
 * // 获取
 * const data = buyProductNow.get()
 * // 移除
 * buyProductNow.remove()
 */
export const buyProductNow = storage('PS_CCMS_BUY_NOW_PRODUCT', true)

/**
 * 购物车购买的商品存储
 * @description 使用 localStorage 持久化
 * @example
 * // 设置
 * buyProductCart.set([{ skuId: '123', count: 1 }])
 * // 获取
 * const list = buyProductCart.get()
 * // 移除
 * buyProductCart.remove()
 */
export const buyProductCart = storage('PS_CCMS_BUY_CART_PRODUCT', true)

/**
 * 立即购买的商品存储（会话期）
 * @description 使用 sessionStorage 持久化
 * @example
 * // 设置
 * buyProductNowSession.set({ skuId: '123' })
 * // 获取
 * const data = buyProductNowSession.get()
 * // 移除
 * buyProductNowSession.remove()
 */
export const buyProductNowSession = storage('PS_CCMS_BUY_NOW_PRODUCT', false)

/**
 * 购物车购买的商品存储（会话期）
 * @description 使用 sessionStorage 持久化
 * @example
 * // 设置
 * buyProductCartSession.set([{ skuId: '123', count: 1 }])
 * // 获取
 * const list = buyProductCartSession.get()
 * // 移除
 * buyProductCartSession.remove()
 */
export const buyProductCartSession = storage('PS_CCMS_BUY_CART_PRODUCT', false)

/**
 * 售后商品信息存储（会话期）
 * @description 使用 sessionStorage 持久化
 * @example
 * // 设置
 * afterSalesProduct.set({ skuId: '123', reason: '质量问题' })
 * // 获取
 * const info = afterSalesProduct.get()
 * // 移除
 * afterSalesProduct.remove()
 */
export const afterSalesProduct = storage('PS_CCMS_AFTER_SALES_PRODUCT', false)
