/**
 * 主题切换工具：通过给根节点添加/移除类名，覆盖 :root 中的默认 CSS 变量
 * 默认(:root)为沃橙主题；当 bizCode === 'ygjd' 时启用京东主题 .theme-jd
 * 注：将类加在 html 元素上，作用域最广且就近覆盖 :root 变量，优先级天然高于默认。
 */

const THEME_CLASS_JD = 'theme-jd';

/**
 * 根据业务编码应用主题
 * @param {string} bizCode
 * @example
 * applyThemeByBizCode('ygjd') // 应用京东主题
 * applyThemeByBizCode('ziying') // 恢复默认主题
 */
export function applyThemeByBizCode(bizCode) {
  const rootEl = document.documentElement; // html
  if (!rootEl) return;

  if (bizCode === 'ygjd') {
    if (!rootEl.classList.contains(THEME_CLASS_JD)) {
      rootEl.classList.add(THEME_CLASS_JD);
    }
  } else {
    if (rootEl.classList.contains(THEME_CLASS_JD)) {
      rootEl.classList.remove(THEME_CLASS_JD);
    }
  }
}

/**
 * 初始化主题（可在应用启动时调用一次）
 * @param {() => string} getBizCode 同步函数，返回当前 bizCode
 * @example
 * import { getBizCode } from '@/utils/curEnv'
 * initTheme(() => getBizCode())
 */
export function initTheme(getBizCode) {
  try {
    const code = typeof getBizCode === 'function' ? getBizCode() : undefined;
    if (code) applyThemeByBizCode(code);
  } catch (e) {
    // 静默失败，不影响启动

    console.warn('[themeManager] initTheme error:', e);
  }
}
