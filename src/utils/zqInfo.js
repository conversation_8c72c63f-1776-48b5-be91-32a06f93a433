/**
 * 政企信息处理模块
 * 提供政企相关信息的获取和处理功能
 */
import { getZqInfo } from 'commonkit'
import { zqRole } from '@utils/storage.js'
import { find } from "es-toolkit/compat";

/**
 * 角色类型常量
 * @enum {string}
 */
const ROLE_TYPE = {
  ENTERPRISE_MANAGER: '1', // 企业经办人
  CUSTOMER_MANAGER: '2',   // 客户经理
  NO_PERMISSION: '-1',     // 无权限
  CANNOT_DISPLAY: '-2',     // 无法展示
  WHITE_USER: '4'          // 白名单用户
}

/**
 * 获取客户经理信息
 * @returns {Object|undefined} 客户经理信息对象，如果不存在则返回undefined
 * @example
 * const cm = getCustomerManagerInfo()
 * if (cm) {
 *   console.log('客户经理:', cm)
 * }
 */
export const getCustomerManagerInfo = () => {
  const info = getZqInfo() || []
  return find(info,item => item.roleType === ROLE_TYPE.CUSTOMER_MANAGER)
}

/**
 * 获取企业经办人信息
 * @returns {Object|undefined} 企业经办人信息对象，如果不存在则返回undefined
 * @example
 * const em = getEnterpriseManagerInfo()
 * if (em) {
 *   console.log('企业经办人:', em)
 * }
 */
export const getEnterpriseManagerInfo = () => {
  const info = getZqInfo() || []
  return find(info,item => item.roleType === ROLE_TYPE.ENTERPRISE_MANAGER || item.roleType === ROLE_TYPE.WHITE_USER)
}

/**
 * 获取政企信息
 * @returns {Object} 政企信息对象
 * @property {string} roleType 角色类型
 *   - '-2' - 无法展示（session有多条权限信息，但没有读取到应展示那个权限）
 *   - '-1' - 无权限（session中无权限）
 *   - '1' - 企业经办人
 *   - '2' - 客户经理
 * @example
 * const info = queryZqInfo()
 * if (info.roleType === '1') {
 *   // 企业经办人
 * }
 */
export const queryZqInfo = () => {
  const info = getZqInfo() || []

  // 根据信息条数处理不同情况
  if (info.length === 0) {
    // 无权限
    return { roleType: ROLE_TYPE.NO_PERMISSION }
  }

  if (info.length === 1) {
    // 单个权限，直接返回
    return info[0]
  }

  if (info.length === 2) {
    // 多个权限，根据URL参数决定角色
    const role = zqRole.get()

    if (role === ROLE_TYPE.ENTERPRISE_MANAGER || role === ROLE_TYPE.WHITE_USER) {
      return getEnterpriseManagerInfo()
    }

    if (role === ROLE_TYPE.CUSTOMER_MANAGER) {
      return getCustomerManagerInfo()
    }

    // 未指定角色
    return { roleType: ROLE_TYPE.CANNOT_DISPLAY }
  }

  // 默认返回无法展示
  return { roleType: ROLE_TYPE.CANNOT_DISPLAY }
}
