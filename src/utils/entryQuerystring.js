/**
 * 入口参数处理模块
 * 处理URL参数并设置相应的存储值
 */
import { parse } from 'qs'
import {isWopay, isUnicom, urlAppend} from 'commonkit'
import {
  getBizCode
} from '@/utils/curEnv'
import {queryZqInfo} from "@utils/zqInfo.js";
import { curChannelBiz, curDeveloperId, curDistriBiz, loginType, zqRole } from '@utils/storage.js'

/**
 * 处理入口URL参数
 * 解析 URL 查询串并设置分销业务码、渠道码、发展人ID、政企登录类型等缓存
 *
 * @param {string} [search] - URL查询字符串，默认使用当前页面的查询字符串
 * @returns {void}
 *
 * @example
 * // 使用当前地址栏
 * setEntryQuerystring()
 * // 传入自定义查询串
 * setEntryQuerystring('distri_biz_code=ziying&source=226&developer_id=10001')
 */
export const setEntryQuerystring = search => {
  // 获取查询字符串
  search = search || window.location.search.substring(1)
  const querystringObject = parse(search)
  const path = window.location.pathname

  // 处理分销业务码
  const PS_CCMS_DISTRI_BIZ = querystringObject.distri_biz_code
  if (PS_CCMS_DISTRI_BIZ) {
    curDistriBiz.set(PS_CCMS_DISTRI_BIZ)
  } else {
    curDistriBiz.set('ziying') // 兜底
  }


  // 特殊路径处理
  if (path.indexOf('/digitalVillage/') >= 0) {
    // 数字乡村特殊处理
    curDistriBiz.set('szxc')
  } else if (path.indexOf('/jdzy/') >= 0) {
    // 京东开普勒特殊处理
    curDistriBiz.set('jdkpl')
  }

  // 政企渠道处理
  if (PS_CCMS_DISTRI_BIZ === 'zq') {
    loginType.set('1')
    const role = querystringObject.role
    if (role === '1' || role === '2') {
      zqRole.set(role)
    }
  }

  // 处理渠道码
  const PS_CCMS_CHANNEL_BIZ = querystringObject.source ||
                              querystringObject.biz_channel_code ||
                              curChannelBiz.get()

  // 根据不同条件设置渠道码
  if (isWopay) {
    curChannelBiz.set('212')
  } else if (isUnicom) {
    curChannelBiz.set('225')
  } else if (PS_CCMS_CHANNEL_BIZ) {
    curChannelBiz.set(PS_CCMS_CHANNEL_BIZ)
  } else {
    curChannelBiz.set('226') // 默认渠道码
  }

  // 处理发展人ID
  const PS_CCMS_DEVELOPER_ID = querystringObject.developer_id || querystringObject.developerId
  if (PS_CCMS_DEVELOPER_ID) {
    curDeveloperId.set(PS_CCMS_DEVELOPER_ID)
  }
}


/**
 * 处理URL参数，确保包含必要的业务代码参数
 * @param {Object} to - 目标路由对象
 */
export const handleUrlParameters = (to) => {
  const host = window.location.origin
  const path = import.meta.env.VITE_BASE_URL
  const baseUrl = host + path + to.fullPath
  const bizCode = getBizCode()
  let callbackUrl = baseUrl
  let needUpdateUrl = false

  // 处理政企商城特殊逻辑
  if (bizCode === 'zq' && !to.query.isv && to.path !== '/login') {
    const zqInfo = queryZqInfo()
    callbackUrl = urlAppend(baseUrl, {
      distri_biz_code: bizCode,
      isv: zqInfo.isvList && zqInfo.isvList.length > 0 ? zqInfo.isvList[0].isvId : ''
    })
    needUpdateUrl = true
  }
  // 处理普通情况下缺少distri_biz_code参数
  else if (!to.query.distri_biz_code) {
    callbackUrl = urlAppend(baseUrl, { distri_biz_code: bizCode })
    needUpdateUrl = true
  }

  // 只在需要时更新URL
  if (needUpdateUrl) {
    // 保留现有的 history.state 值，避免 Vue Router 警告
    history.replaceState(history.state, '', callbackUrl)
  }
}

