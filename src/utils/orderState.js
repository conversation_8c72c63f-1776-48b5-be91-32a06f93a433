/**
 * 订单状态码映射表
 * @type {Object<string, string>}
 */
const ORDER_STATE_MAP = {
  '0': '待付款',
  '1': '待发货',
  '2': '已取消',
  '3': '待发货',
  '4': '部分发货',
  '5': '配送中',
  '6': '部分撤销',
  '7': '拒收',
  '8': '已撤销',
  '9': '已签收',
  '10': '已退款',
  '11': '部分退款',
  '12': '部分退款中',
  '-1': '已删除'
}

/**
 * 将订单状态码转换为中文描述
 * @param {string|number} state - 订单状态码
 * @returns {string} 状态描述，如果状态码不存在则返回空字符串
 * @example
 * orderState('0') // '待付款'
 * orderState(9)   // '已签收'
 */
export default function orderState(state) {
  return ORDER_STATE_MAP[String(state)] || ''
}
