import { showToast } from 'vant'
import useClipboard from 'vue-clipboard3'

/**
 * 复制文本内容到系统剪贴板
 * 使用 vue-clipboard3 库实现跨浏览器的剪贴板操作
 * 复制成功或失败时会显示相应的 Toast 提示信息
 *
 * @param {string} text 需要复制到剪贴板的文本内容
 * @returns {Promise<boolean>} 返回 Promise，resolve 为 true 表示复制成功，false 表示复制失败
 *
 * @example
 * // 复制链接
 * copy('https://example.com').then(success => {
 *   if (success) {
 *     console.log('复制成功')
 *   }
 * })
 *
 * // 复制普通文本
 * await copy('Hello World')
 */
export const copy = async (text) => {
  const { toClipboard } = useClipboard()
  try {
    // 执行剪贴板复制操作
    await toClipboard(text)
    // 显示复制成功提示
    showToast('链接已复制，赶快分享吧！~')
    return true
  } catch (error) {
    // 记录复制失败的错误信息
    logger.error('复制失败', error)
    // 显示复制失败提示
    showToast('设备不支持复制功能，请手动复制...')
    return false
  }
}
