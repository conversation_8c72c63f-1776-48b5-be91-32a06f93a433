/**
 * @fileoverview 导航推广状态管理模块
 * @description 管理导航推广相关的状态信息，包括推广菜单项的获取和设置
 */

import { defineStore } from 'pinia'
import { getIconInfo } from '@api/interface/bannerIcon.js'

/**
 * 导航推广状态管理 Store
 * @description 用于管理导航推广菜单项的状态和相关操作
 * @function useNavPromotionStore
 * @returns {Object} Pinia store 实例
 * @example
 * import { useNavPromotionStore } from '@/store/modules/navPromotion'
 * const navPromotionStore = useNavPromotionStore()
 * // 查询推广菜单项
 * await navPromotionStore.query({ bizCode: 'example' })
 * // 手动设置菜单项
 * navPromotionStore.setMenuItem({ title: '推广标题', path: '/promotion', img: 'icon.png', activeImg: 'active-icon.png', className: 'promotion-icon' })
 */
export const useNavPromotionStore = defineStore('navPromotion', {
  /**
   * 导航推广状态
   * @returns {Object} 导航推广相关的状态对象
   */
  state: () => ({
    /**
     * 推广菜单项
     * @type {Object|null}
     * @description 存储推广菜单项的详细信息
     * @property {string} title - 菜单标题
     * @property {string} path - 菜单链接路径
     * @property {string} img - 菜单图标URL
     * @property {string} activeImg - 激活状态图标URL
     * @property {string} className - CSS类名
     */
    menuItem: null
  }),
  actions: {
    /**
     * 设置推广菜单项
     * @description 更新推广菜单项的状态
     * @param {Object} payload - 菜单项数据
     * @param {string} payload.title - 菜单标题
     * @param {string} payload.path - 菜单链接路径
     * @param {string} payload.img - 菜单图标URL
     * @param {string} payload.activeImg - 激活状态图标URL
     * @param {string} payload.className - CSS类名
     * @example
     * ```javascript
     * setMenuItem({
     *   title: '推广活动',
     *   path: '/promotion/activity',
     *   img: 'promotion.png',
     *   activeImg: 'promotion-active.png',
     *   className: 'promotion-icon'
     * })
     * ```
     */
    setMenuItem(payload) {
      this.menuItem = payload
    },
    /**
     * 查询推广菜单项
     * @description 根据业务代码异步获取推广菜单项信息，如果已存在则直接返回缓存数据
     * @param {Object} params - 查询参数
     * @param {string} params.bizCode - 业务代码，用于获取对应的推广信息
     * @returns {Promise<Object>} 返回推广菜单项对象
     * @example
     * ```javascript
     * // 查询特定业务的推广菜单项
     * const menuItem = await query({ bizCode: 'PROMOTION_001' })
     * console.log(menuItem)
     * // {
     * //   title: '',
     * //   path: '/promotion/detail',
     * //   img: 'https://example.com/icon.png',
     * //   activeImg: '',
     * //   className: 'promotion-icon'
     * // }
     * ```
     */
    async query({ bizCode }) {
      if (this.menuItem) return this.menuItem
      const [, json] = await getIconInfo({ bizCode, showPage: 3 })
      let menuItem = {}
      if (json && json.length > 0) {
        const menuList = json[0]
        menuItem = {
          title: '',
          path: menuList.url,
          img: menuList.imgUrl,
          activeImg: '',
          className: 'promotion-icon'
        }
      }
      this.setMenuItem(menuItem)
      return menuItem
    }
  }
})
