/**
 * @fileoverview 京东自营状态管理模块
 * @description 管理京东自营相关的状态信息，主要用于控制 tabbar 的显示状态
 */

import { defineStore } from 'pinia'

/**
 * 京东自营状态管理 Store
 * @description 用于管理京东自营页面的 tabbar 状态
 * @function useJdzyStore
 * @returns {Object} Pinia store 实例
 * @example
 * import { useJdzyStore } from '@/store/modules/jdzy'
 * const jdzyStore = useJdzyStore()
 * jdzyStore.changeJDtabbar('1') // 切换 tabbar 状态
 * console.log(jdzyStore.JDtabbar) // '1'
 */
export const useJdzyStore = defineStore('jdzy', {
  /**
   * 京东自营状态
   * @returns {Object} 京东自营相关的状态对象
   */
  state: () => ({
    /**
     * 京东tabbar状态
     * @type {string}
     * @description 控制京东自营页面tabbar的显示状态，'0'表示默认状态，'1'表示激活状态
     */
    JDtabbar: '0'
  }),
  actions: {
    /**
     * 切换京东 tabbar 状态
     * @description 更新京东自营页面的 tabbar 显示状态
     * @param {string} payload 新的 tabbar 状态值（'0' | '1'）
     * @returns {void}
     * @example
     * // 设置为激活状态
     * changeJDtabbar('1')
     * // 设置为默认状态
     * changeJDtabbar('0')
     */
    changeJDtabbar(payload) {
      // 更新 tabbar 状态
      this.JDtabbar = payload
    }
  }
})
