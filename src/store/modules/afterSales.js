/**
 * @fileoverview 售后服务状态管理模块
 * @description 管理售后服务相关的状态信息，包括申请类型、售后状态、订单信息等
 */

import { defineStore } from 'pinia'

/**
 * 售后服务状态管理 Store
 * @description 用于管理售后服务流程中的各种状态信息
 * @function useAfterSalesStore
 * @returns {Object} Pinia store 实例
 * @example
 * import { useAfterSalesStore } from '@/store/modules/afterSales'
 * const afterSalesStore = useAfterSalesStore()
 * // 更新售后信息
 * afterSalesStore.updateAfterSalesInfo({ applyType: 'refund', afterSaleState: 'processing', bizOrderId: '123456' })
 * // 获取售后信息
 * const info = afterSalesStore.getAfterSalesInfo
 */
export const useAfterSalesStore = defineStore('afterSales', {
  /**
   * 售后服务状态
   * @returns {Object} 售后服务相关的状态对象
   */
  state: () => ({
    /**
     * 申请类型
     * @type {string}
     * @description 售后申请的类型，如退款、换货、维修等
     */
    applyType: '',

    /**
     * 售后状态
     * @type {string}
     * @description 当前售后申请的处理状态
     */
    afterSaleState: '',

    /**
     * 业务订单ID
     * @type {string}
     * @description 关联的业务订单唯一标识
     */
    bizOrderId: '',

    /**
     * 业务代码
     * @type {string}
     * @description 业务类型标识代码
     */
    bizCode: '',

    /**
     * 订单状态
     * @type {string}
     * @description 订单的当前状态
     */
    orderState: ''
  }),

  actions: {
    /**
     * 更新售后服务信息
     * @description 批量更新售后服务相关的状态信息，只更新传入的字段
     * @param {Object} payload - 要更新的售后信息对象
     * @param {string} [payload.applyType] - 申请类型
     * @param {string} [payload.afterSaleState] - 售后状态
     * @param {string} [payload.bizOrderId] - 业务订单ID
     * @param {string} [payload.bizCode] - 业务代码
     * @param {string} [payload.orderState] - 订单状态
     * @example
     * // 更新部分信息
     * updateAfterSalesInfo({ applyType: 'refund', afterSaleState: 'approved' })
     * // 更新全部信息
     * updateAfterSalesInfo({ applyType: 'exchange', afterSaleState: 'processing', bizOrderId: 'ORD123456', bizCode: 'MALL', orderState: 'confirmed' })
     */
    updateAfterSalesInfo(payload) {
      if (payload.applyType !== undefined) this.applyType = payload.applyType
      if (payload.afterSaleState !== undefined) this.afterSaleState = payload.afterSaleState
      if (payload.bizOrderId !== undefined) this.bizOrderId = payload.bizOrderId
      if (payload.bizCode !== undefined) this.bizCode = payload.bizCode
      if (payload.orderState !== undefined) this.orderState = payload.orderState
    }
  },

  getters: {
    /**
     * 获取完整的售后服务信息
     * @description 返回当前store中所有的售后服务状态信息
     * @param {Object} state - 当前状态对象
     * @returns {Object} 包含所有售后服务信息的对象
     * @example
     * const afterSalesInfo = afterSalesStore.getAfterSalesInfo
     * console.log(afterSalesInfo)
     * // { applyType: 'refund', afterSaleState: 'processing', bizOrderId: 'ORD123456', bizCode: 'MALL', orderState: 'confirmed' }
     */
    getAfterSalesInfo: (state) => state
  }
})
