import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { isIos } from 'commonkit'
import './assets/css/common.less'
import App from './App.vue'
import router from './router'
import { registerPlugins } from './plugins'
import { initializeApp, fixEnvironmentIssues } from './initialization'
import WoAlertPlugin from '@/components/WoElementCom/WoAlert'
import 'intersection-observer-polyfill';
import { initTheme } from '@/utils/themeManager'
import { getBizCode } from '@/utils/curEnv'
// 引入全局Logger系统
import log from '@/utils/logger'
// 特殊处理 ResizeObserver
if (typeof window.ResizeObserver === 'undefined') {
  import('resize-observer-polyfill').then(module => {
    window.ResizeObserver = module.default;
  });
}
const app = createApp(App)
// 初始化应用
initializeApp()
// 初始化主题（根据 bizCode 设置 html 上的主题类）
initTheme(getBizCode)
// 修复环境问题
fixEnvironmentIssues()

log.info('应用初始化完成')


app.use(createPinia())
app.use(router)
app.use(WoAlertPlugin)
// 注册插件
registerPlugins(app)
app.mount('#app')

// 解决ios下返回上一页白屏问题
window.onpageshow = function (e) {
  if (isIos && e.persisted) {
    window.location.reload()
  }
}
