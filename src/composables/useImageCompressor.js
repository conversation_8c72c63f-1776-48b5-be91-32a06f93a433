import compressImageFile from '@utils/imgZip.js'
import { ref } from 'vue'
import { merge } from 'es-toolkit'

/**
 * 图片压缩组合函数
 * 提供图片压缩功能，包括压缩状态管理和文件预处理
 * @returns {Object} 包含压缩相关状态和方法的对象
 */
export function useImageCompressor() {
  // 压缩操作的加载状态
  const loading = ref(false)
  // 压缩结果
  const result = ref(null)
  // 压缩过程中的错误信息
  const error = ref(null)

  /**
   * 压缩图片文件
   * @param {File} file - 要压缩的图片文件
   * @param {Object} options - 压缩选项配置
   * @returns {Promise<Object|null>} 压缩结果对象，失败时返回null
   */
  const compress = async (file, options = {}) => {
    loading.value = true
    error.value = null
    try {
      const mergedOptions = merge({ file }, options)
      const res = await compressImageFile(mergedOptions)
      result.value = res
      loading.value = false
      return res
    } catch (err) {
      error.value = err
      loading.value = false
      return null
    }
  }

  /**
   * 文件读取前的预处理方法
   * 对上传的图片文件进行压缩和验证处理
   * @param {File} file - 要处理的图片文件
   * @param {Object} options - 处理选项配置
   * @param {number} options.maxSize - 最大文件大小限制（MB），默认10MB
   * @param {boolean} options.showLoadingMessage - 是否显示加载消息，默认true
   * @param {string} options.loadingMessage - 加载时显示的消息，默认'处理中...'
   * @param {Function} options.showToast - 显示提示消息的方法
   * @param {Function} options.showLoadingToast - 显示加载提示的方法
   * @param {Function} options.closeToast - 关闭提示的方法
   * @returns {Promise<File|false>} 处理后的文件对象，验证失败时返回false
   */
  const beforeRead = async (file, options = {}) => {
    const {
      maxSize = 10,
      showLoadingMessage = true,
      loadingMessage = '处理中...',
      showToast,
      showLoadingToast,
      closeToast
    } = options

    const fileSizeBefore = file.size / 1024 / 1024 // 转换为MB
    logger.warn('fileSizeBefore', fileSizeBefore.toFixed(2) + 'M')

    // 显示加载提示
    if (showLoadingMessage) {
      showLoadingToast({
        message: loadingMessage,
        forbidClick: true
      })
    } else {
      showLoadingToast()
    }

    const startTime = new Date()
    const newFileObj = await compressImageFile({ file })
    const endTime = new Date()
    const executionTime = (endTime - startTime) / 1000
    logger.log(`压缩结束执行时间为：${executionTime}秒`)
    closeToast()

    const { file: newFile } = newFileObj
    const fileSizeAfter = newFile.size / 1024 / 1024 // 转换为MB
    logger.warn('fileSizeAfter', fileSizeAfter.toFixed(2) + 'M')

    // 检查压缩后文件大小
    if (fileSizeAfter > maxSize) {
      showToast(`图片大小不能超过 ${maxSize}M`)
      return false
    }

    logger.warn('fileSizeBefore', fileSizeBefore)

    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']
    if (newFile instanceof Array && newFile.length) {
      const isValidType = allowedTypes.some(type => type === newFile.type)
      if (!isValidType) {
        showToast('请选择正确图片格式上传')
        return false
      }
      return newFile
    } else {
      const isValidType = allowedTypes.some(type => type === newFile.type)
      if (!isValidType) {
        showToast('请选择正确图片格式上传')
        return false
      }
      return newFile
    }
  }

  return {
    loading,
    result,
    error,
    compress,
    beforeRead
  }
}
