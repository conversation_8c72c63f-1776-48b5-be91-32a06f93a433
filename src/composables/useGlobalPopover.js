import { ref } from 'vue'
import { createGlobalState } from '@vueuse/core'
import { isEqual } from 'es-toolkit'

/**
 * 全局弹出层状态管理组合函数
 * 提供统一的弹出层状态管理，确保同一时间只有一个弹出层处于激活状态
 * @returns {Object} 包含弹出层状态管理方法的对象
 * @property {Ref<string|number|null>} activePopoverId 当前激活的弹出层ID
 * @property {(id: string|number|null) => void} setActivePopover 设置激活的弹出层
 * @property {() => void} closeAllPopovers 关闭所有弹出层
 * @property {(id: string|number|null) => boolean} isPopoverActive 指定弹出层是否激活
 * @example
 * const { setActivePopover, isPopoverActive, closeAllPopovers } = useGlobalPopover()
 * setActivePopover('menu')
 * if (isPopoverActive('menu'))
 *   closeAllPopovers()
 */
export const useGlobalPopover = createGlobalState(() => {
  /** 当前激活的弹出层ID */
  const activePopoverId = ref(null)

  /**
   * 设置激活的弹出层
   * @param {string|number|null} id - 弹出层的唯一标识符
   */
  const setActivePopover = (id) => {
    activePopoverId.value = id
  }

  /**
   * 关闭所有弹出层
   * 将激活的弹出层ID重置为null
   */
  const closeAllPopovers = () => {
    activePopoverId.value = null
  }

  /**
   * 检查指定弹出层是否处于激活状态
   * @param {string|number|null} id - 要检查的弹出层ID
   * @returns {boolean} 如果指定弹出层处于激活状态则返回true，否则返回false
   */
  const isPopoverActive = (id) => {
    return isEqual(activePopoverId.value, id)
  }

  return {
    activePopoverId,
    setActivePopover,
    closeAllPopovers,
    isPopoverActive
  }
})
