import { getCurrentInstance } from 'vue'
import { get } from 'es-toolkit/compat'

/**
 * 获取Vue实例的全局代理对象
 * 通过getCurrentInstance获取当前组件实例的代理对象，用于访问全局属性和方法
 * @returns {Object|undefined} Vue实例的代理对象，如果获取失败则返回undefined
 * @example
 * const proxy = useGlobalProxy()
 * proxy?.$router?.push('/')
 */
export function useGlobalProxy() {
  const instance = getCurrentInstance()
  return get(instance, 'proxy')
}

/**
 * 获取全局提示方法
 * 从全局代理对象中提取$alert方法，用于显示提示信息
 * @returns {Function|undefined} 全局提示方法，如果不存在则返回undefined
 * @example
 * const $alert = useAlert()
 * $alert?.('标题', '内容')
 */
export function useAlert() {
  const proxy = useGlobalProxy()
  return get(proxy, '$alert')
}
