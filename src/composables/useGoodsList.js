import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@store/modules/user.js'
import { useNewCartStore } from '@store/modules/newCart.js'
import { getBizCode } from '@utils/curEnv.js'
import { addOneClick } from '@api/index.js'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { pick, compact, debounce, isNil } from 'es-toolkit'
import { get, defaultTo, some, filter, map } from 'es-toolkit/compat'
/**
 * 商品列表管理组合式函数
 * 提供商品列表加载、筛选、跳转与加入购物车等能力
 *
 * @returns {Object} 商品列表相关的状态、计算属性和方法
 * @property {Ref<Array>} goodsList
 * @property {Ref<boolean>} loading
 * @property {Ref<boolean>} finished
 * @property {Ref<boolean>} isLoading
 * @property {Ref<number>} pageNo
 * @property {Ref<number>} pageSize
 * @property {Ref<Object>} filterCriteria
 * @property {ComputedRef<boolean>} hasFilterConditions
 * @property {ComputedRef<Object>} curAddrInfo
 * @property {ComputedRef<string>} locationText
 * @property {ComputedRef<string>} addressInfo
 * @property {Function} resetList
 * @property {Function} processGoodsData
 * @property {Function} applyStockFilter
 * @property {Function} goToDetail
 * @property {Function} goToCart
 * @property {Function} addOneCart
 * @property {Function} handleFilterConfirm
 * @property {Function} handleFilterReset
 * @property {Function} handleAddressChanged
 *
 * @example
 * // 基本用法
 * const {
 *   goodsList, loading, finished,
 *   resetList, addOneCart, goToDetail
 * } = useGoodsList()
 *
 * // 触发一键加购
 * // await addOneCart(item)
 */
export function useGoodsList() {
  const userStore = useUserStore()
  const cartStore = useNewCartStore()
  const router = useRouter()

  // 商品列表数据
  const goodsList = ref([])
  // 加载状态
  const loading = ref(false)
  // 是否加载完成
  const finished = ref(false)
  // 初始加载状态
  const isLoading = ref(true)
  // 当前页码
  const pageNo = ref(1)
  // 每页数量
  const pageSize = ref(10)

  // 筛选条件配置
  const filterCriteria = ref({
    isStock: true,
    minPrice: '',
    maxPrice: '',
    brandsList: []
  })

  /**
   * 检查是否有筛选条件
   * @returns {boolean} 是否存在筛选条件
   */
  const hasFilterConditions = computed(() => {
    const { isStock, minPrice, maxPrice, brandsList } = filterCriteria.value
    return isStock ||
      minPrice !== '' ||
      maxPrice !== '' ||
      some(brandsList, brand => brand.isSelected)
  })

  /**
   * 当前地址信息
   * @returns {Object} 用户当前地址信息
   */
  const curAddrInfo = computed(() => userStore.curAddressInfo)

  /**
   * 位置文本显示
   * @returns {string} 地址详情文本
   */
  const locationText = computed(() => {
    return curAddrInfo.value?.addrDetail || ''
  })

  /**
   * 地址信息JSON字符串
   * @returns {string} 序列化的地址信息
   */
  const addressInfo = computed(() => {
    const addressFields = ['provinceId', 'provinceName', 'cityId', 'cityName', 'countyId', 'countyName', 'townId', 'townName']
    return JSON.stringify(pick(curAddrInfo.value, addressFields))
  })

  /**
   * 重置列表状态
   */
  const resetList = () => {
    pageNo.value = 1
    finished.value = false
    goodsList.value = []
  }

  /**
   * 处理商品数据
   * @param {Array} items - 原始商品数据数组
   * @returns {Array} 处理后的商品数据数组
   */
  const processGoodsData = (items) => {
    const bizCode = getBizCode()
    const isZqBiz = bizCode === 'zq'

    return map(items, item => {
      // 处理商品展示图片URL
      item.showImageUrl = item.imageUrl?.split(',')[0] || item.listImageUrl || ''

      // 根据业务类型选择参数数据源
      const paramSource = isZqBiz ? item.skuList?.[0] : item
      item.params = compact([
        paramSource?.param,
        paramSource?.param1,
        paramSource?.param2,
        paramSource?.param3,
        paramSource?.param4
      ])

      // 处理商品销量数据
      item.realSaleVolume = get(item, 'sales', get(item, 'realSaleVolume', 0));

      if (isZqBiz) {
        // 检查价格数据是否缺失
        const hasNoPrice = isNil(item.highPrice) || item.highPrice === '' ||
          isNil(item.lowPrice) || item.lowPrice === ''

        if (hasNoPrice) {
          console.warn(99999,item)
          // 安全获取第一个SKU数据
          const firstSku = get(item, 'skuList[0]')

          // 从SKU获取价格并格式化
          item.highPrice = !isNil(firstSku?.highPrice) ?
            (firstSku.highPrice / 100).toFixed(2) :
            defaultTo(item.highPrice, '')

          item.lowPrice = !isNil(firstSku?.lowPrice) ?
            (firstSku.lowPrice / 100).toFixed(2) :
            defaultTo(item.lowPrice, '')
        } else {
          console.warn(8888)
          // 价格格式化处理函数
          const formatPrice = (price) => !isNil(price) ? (price / 100).toFixed(2) : ''

          item.highPrice = formatPrice(item.highPrice)
          item.lowPrice = formatPrice(item.lowPrice)
        }
      }
      return item
    })
  }

  /**
   * 应用库存筛选
   * @param {Array} items - 商品数据数组
   * @returns {Array} 筛选后的商品数据数组
   */
  const applyStockFilter = (items) => {
    if (filterCriteria.value.isStock) {
      return filter(items, item => item.stock > 0)
    }
    return items
  }

  /**
   * 跳转到商品详情页
   * @param {Object} item - 商品信息对象
   */
  const goToDetail = (item) => {
    router.push(`/goodsdetail/${item.goodsId}/${item.skuId}`)
  }

  /**
   * 跳转到购物车页面
   */
  const goToCart = () => {
    router.push('/cart')
  }

  /**
   * 一键加入购物车（防抖处理）
   * @param {Object} item - 商品信息对象
   */
  const addOneCart = debounce(async (item) => {
    try {
      showLoadingToast()

      const [err] = await addOneClick({
        bizCode: getBizCode('ORDER'),
        skuId: item.skuId,
        goodsId: item.goodsId,
        addressInfo: addressInfo.value
      })

      closeToast()

      if (err) {
        logger.error('一键加入购物车失败:', err.msg)
        showToast(err.msg)
        return
      }

      showToast('加入购物车成功！')
      // 更新购物车数据
      await cartStore.query()
    } catch (error) {
      closeToast()
      logger.error('加入购物车失败:', error)
      showToast('加入购物车失败，请重试')
    }
  }, 300)

  /**
   * 处理筛选确认
   * @param {Object} criteria - 筛选条件
   * @param {Function} fetchFunction - 获取数据的函数
   */
  const handleFilterConfirm = (criteria, fetchFunction) => {
    resetList()
    if (typeof fetchFunction === 'function') {
      fetchFunction()
    } else {
      logger.warn('handleFilterConfirm: 非函数')
    }
  }

  /**
   * 处理筛选重置
   */
  const handleFilterReset = () => {
    logger.log('重置筛选条件')
  }

  /**
   * 处理地址变更
   * @param {Function} fetchFunction - 获取数据的函数
   */
  const handleAddressChanged = (fetchFunction) => {
    resetList()
    if (typeof fetchFunction === 'function') {
      fetchFunction()
    } else {
      logger.warn('handleAddressChanged: 非函数')
    }
  }

  return {
    // 状态
    goodsList,
    loading,
    finished,
    isLoading,
    pageNo,
    pageSize,
    filterCriteria,

    // 计算属性
    hasFilterConditions,
    curAddrInfo,
    locationText,
    addressInfo,

    // 方法
    resetList,
    processGoodsData,
    applyStockFilter,
    goToDetail,
    goToCart,
    addOneCart,
    handleFilterConfirm,
    handleFilterReset,
    handleAddressChanged
  }
}
