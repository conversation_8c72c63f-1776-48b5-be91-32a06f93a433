<!--
/**
 * 订单售后详情页面组件
 *
 * 主要功能：
 * 1. 展示售后订单的详细信息，包括退款/退货流程进度和状态展示
 * 2. 提供售后流程步骤可视化，支持不同类型售后的步骤展示和进度跟踪
 * 3. 支持物流信息管理，包括收货地址展示和物流单号填写功能
 * 4. 提供售后操作功能，支持取消退款、取消退货、重新申请等操作
 * 5. 集成客服联系功能，方便用户获取帮助和咨询服务
 * 6. 支持订单号和物流单号的复制功能，提升用户体验和便利性
 * 7. 实现售后申请时效检查，防止过期订单重复申请
 * 8. 支持多种供应商类型的售后流程，包括京东等第三方供应商
 *
 * 技术特点：
 * - 使用响应式数据管理售后详情状态，实时更新页面内容
 * - 采用计算属性动态控制按钮显示逻辑，根据状态智能展示操作
 * - 集成剪贴板功能支持文本复制，提供便捷的信息获取方式
 * - 支持多种售后类型的流程展示，适配不同业务场景
 * - 实现动态步骤进度指示器，直观展示售后处理进度
 * - 集成状态管理和本地存储，保证数据一致性和持久化
 *
 * 使用场景：
 * - 用户查看售后申请的详细进度和状态信息
 * - 用户进行售后相关操作和管理，如取消、重新申请等
 * - 售后流程跟踪和状态更新，实时了解处理进展
 * - 物流信息查看和管理，包括寄回商品的物流跟踪
 */
-->

<template>
  <!-- 售后详情主容器 -->
  <div class="order-after-sales-detail">
    <!-- 骨架屏 -->
    <OrderAfterSalesDetailSkeleton v-if="!dataLoaded" />
    
    <!-- 实际内容 -->
    <div v-else-if="Object.keys(afterSaleDetailInfo).length > 0" class="order-after-sales-detail-content">
    <!-- 售后流程进度区域 -->
    <!-- 展示售后申请的处理步骤和当前状态 -->
    <div class="after-sales-process" v-if="Object.keys(afterSaleDetailInfo).length > 0">
      <!-- 流程步骤指示器 -->
      <!-- 当满足显示条件且不是关闭或拒绝状态时显示步骤 -->
      <div class="steps"
           v-if="cancelSupplierAfterSalRecStep && afterSaleDetailInfo.afterSaleState !== '0' && afterSaleDetailInfo.afterSaleState !== '7'">
        <!-- 步骤项循环渲染 -->
        <!-- 根据当前步骤索引动态设置激活状态 -->
        <div class="steps-item" :class="{ 'active': index < stepIndex }" v-for="(i, index) in stepEnum" :key="i.id">
          <!-- 步骤图标区域 -->
          <div class="item-top">
            <!-- 已完成步骤显示选中图标 -->
            <img v-if="index < stepIndex" class="node-icon selcet" src="./assets/selectNode.png" alt="">
            <!-- 未完成步骤显示未选中图标 -->
            <img v-else class="node-icon no-select" src="./assets/noSelectNode.png" alt="">
          </div>
          <!-- 步骤名称区域 -->
          <div class="item-bottom">
            <span class="node-name">{{ i.title }}</span>
          </div>
        </div>
      </div>
      <!-- 流程内容提示区域 -->
      <!-- 当售后商品信息存在时显示当前步骤的详细说明 -->
      <div class="content" v-if="Object.keys(afterSaleGoodsInfo).length > 0">
        <!-- 当前步骤标题 -->
        <div class="process-title">
          {{ afterSaleDetailInfo.afterSaleStepCurInfo.tipsTitle }}
        </div>
        <!-- 当前步骤详细说明 -->
        <div class="process-tips">
          {{ afterSaleDetailInfo.afterSaleStepCurInfo.tipsSubTitle }}
        </div>
      </div>
    </div>
    <!-- 模块分隔线 -->
    <div class="module-divider"></div>
    <!-- 物流信息填写区域 -->
    <!-- 当售后类型为退货且状态为寄回商品时显示 -->
    <div class="logistics-information" v-if="type === '2' && afterSaleDetailInfo.afterSaleState === '3'">
      <!-- 商家收货地址信息 -->
      <div class="business">
        <!-- 收货地址详情展示 -->
        <div class="business-address">
          <!-- 收货人姓名和电话 -->
          <div class="base">
            <span>收货地址：{{ afterSaleDetailInfo.returnAddressInfo.recName }}</span><span
            class="phone">{{ afterSaleDetailInfo.returnAddressInfo.recPhone }}</span>
          </div>
          <!-- 详细收货地址 -->
          <div class="address">{{ afterSaleDetailInfo.returnAddressInfo.addrDetail }}</div>
          <!-- 退货政策说明 -->
          <div class="return-policy">退货说明：{{ afterSaleDetailInfo.returnPolicy ? afterSaleDetailInfo.returnPolicy
            : '影响二次销售禁止退货' }}</div>
        </div>
        <!-- 复制地址信息按钮 -->
        <!-- 点击复制完整的收货地址信息到剪贴板 -->
        <div class="copy" @click="onClipboardCopy(`${afterSaleDetailInfo.returnAddressInfo.recName + afterSaleDetailInfo.returnAddressInfo.recPhone + afterSaleDetailInfo.returnAddressInfo.addrDetail}`)">
          复制
        </div>
      </div>
      <!-- 用户物流信息填写区域 -->
      <div class="mine-logistics">
        <!-- 物流填写提示信息 -->
        <div class="base">
          <div class="title">
            我已寄出
          </div>
          <div class="tips">
            填写物流单号
          </div>
        </div>
        <!-- 填写物流单号按钮 -->
        <!-- 点击跳转到物流单号填写页面 -->
        <div class="input-logistics-btn" @click="setTrackingNumber">
          填写单号
        </div>
      </div>
    </div>
    <!-- 物流信息区域分隔线 -->
    <div class="module-divider" v-if="type === '2' && afterSaleDetailInfo.afterSaleState === '3'"></div>

    <!-- 物流完成状态展示区域 -->
    <!-- 当售后类型为退货且状态为商家退款或退款完成时显示 -->
    <div class="logistics-compete"
         v-if="type === '2' && (afterSaleDetailInfo.afterSaleState === '4' || afterSaleDetailInfo.afterSaleState === '5')">
      <!-- 物流信息查看单元格 -->
      <div class="wo-cell is-center">
        <!-- 物流单号或状态显示 -->
        <div class="cell-left">
          <div class="left-title">{{ isLogisticsInfo ? afterSaleDetailInfo.orderTrack : '已填写物流单号' }} </div>
        </div>
        <!-- 查看物流详情按钮 -->
        <!-- 点击查看物流跟踪详细信息 -->
        <div class="cell-right" @click="onExpressClick(1)">
          <div class="right-title">查看详情</div>
          <img class="right-arrow" src="./assets/arrow.png" alt="" srcset="">
        </div>
      </div>
    </div>
    <!-- 物流完成区域分隔线 -->
    <div class="module-divider"
         v-if="type === '2' && (afterSaleDetailInfo.afterSaleState === '4' || afterSaleDetailInfo.afterSaleState === '5')">
    </div>
    <!-- 售后信息详情区域 -->
    <!-- 当售后商品信息存在时显示详细的售后信息 -->
    <div class="after-sales-info" v-if="Object.keys(afterSaleGoodsInfo).length > 0">
      <!-- 信息标题 -->
      <div class="info-title">退款信息</div>

      <!-- 售后商品信息展示 -->
      <div class="after-sales-goods">
        <!-- 商品图片 -->
        <img class="goods-img" :src="afterSaleGoodsInfo.detailImageUrl" alt="" srcset="">
        <!-- 商品基本信息 -->
        <div class="goods-info">
          <!-- 商品名称 -->
          <p class="goods-name">{{ afterSaleGoodsInfo.name }}</p>
          <!-- 商品规格 -->
          <p class="goods-spec">{{ afterSaleGoodsInfo.spec }}</p>
        </div>
      </div>

      <!-- 售后详细信息列表 -->
      <div class="info-content">
        <!-- 退款原因信息 -->
        <div class="info-cell">
          <div class="cell-left">
            退款原因
          </div>
          <div class="cell-right">
            {{ afterSaleDetailInfo.refundReason || afterSaleDetailInfo.rejectReason }}
          </div>
        </div>

        <!-- 退款金额信息 -->
        <!-- 根据售后状态显示申请金额或实际退款金额 -->
        <div class="info-cell">
          <div class="cell-left">
            退款金额
          </div>
          <div class="cell-right">
            ￥{{ afterSaleDetailInfo.afterSaleState !== '6' ? afterSaleDetailInfo.applyRefundMoney :
            afterSaleDetailInfo.refundMoney }}
          </div>
        </div>

        <!-- 申请件数信息 -->
        <div class="info-cell">
          <div class="cell-left">
            申请件数
          </div>
          <div class="cell-right">
            {{ afterSaleDetailInfo.applyRefundSkuNum || afterSaleDetailInfo.applyRejectSkuNum }}件
          </div>
        </div>

        <!-- 申请时间信息 -->
        <div class="info-cell">
          <div class="cell-left">
            申请时间
          </div>
          <div class="cell-right">
            {{ afterSaleDetailInfo.applyDate }}
          </div>
        </div>

        <!-- 取消时间信息（条件显示） -->
        <div class="info-cell" v-if="afterSaleDetailInfo.cancelDate">
          <div class="cell-left">
            取消时间
          </div>
          <div class="cell-right">
            {{ afterSaleDetailInfo.cancelDate }}
          </div>
        </div>

        <!-- 退款时间信息（条件显示） -->
        <div class="info-cell" v-if="afterSaleDetailInfo.refundDate">
          <div class="cell-left">
            退款时间
          </div>
          <div class="cell-right">
            {{ afterSaleDetailInfo.refundDate }}
          </div>
        </div>

        <!-- 服务单号信息 -->
        <div class="info-cell">
          <div class="cell-left">
            服务单号
          </div>
          <div class="cell-right">
            {{ afterSaleDetailInfo.id }}
          </div>
        </div>

        <!-- 京东订单号信息（京东供应商时显示） -->
        <div class="info-cell" v-if="isJD && afterSaleDetailInfo.supplierOutOrderId">
          <div class="cell-left">
            京东订单号
          </div>
          <div class="cell-right">
             <span>{{ afterSaleDetailInfo.supplierOutOrderId }}</span>
            <!-- 复制京东订单号按钮 -->
            <span class="copy" @click="copyText(afterSaleDetailInfo.supplierOutOrderId)">复制</span>
          </div>
        </div>

        <!-- 支付订单号信息 -->
        <div class="info-cell">
          <div class="cell-left">
            支付订单号
          </div>
          <div class="cell-right">
            <span>{{ afterSaleDetailInfo.bizOrderId }}</span>
            <!-- 复制支付订单号按钮 -->
            <span class="copy" @click="copyText(afterSaleDetailInfo.bizOrderId)">
              复制
            </span>
          </div>
        </div>
      </div>
    </div>
    <!-- 操作按钮区域 -->
    <!-- 当售后详情信息存在且数据加载完成时显示操作按钮 -->
    <div class="op" v-if="Object.keys(afterSaleDetailInfo).length > 0 && dataLoaded">
      <!-- 联系客服按钮 -->
      <!-- 始终显示，用户可随时联系客服获取帮助 -->
      <WoButton size="medium"  @click="onHotLineClick">联系客服</WoButton>

      <!-- 取消退款按钮 -->
      <!-- 当满足取消退款条件时显示 -->
      <WoButton size="medium"  type="primary"   v-if="showCancelRefundButton" @click="cancelRefund">取消退款</WoButton>

      <!-- 取消退货按钮 -->
      <!-- 当满足取消退货条件时显示 -->
      <WoButton size="medium"  type="primary" v-if="showCancelReturnButton" @click="cancelReturn">取消退货</WoButton>

      <!-- 重新申请按钮 -->
      <!-- 当满足重新申请条件时显示 -->
      <WoButton size="medium"  type="primary"  v-if="showReapplyButton" @click="reapply">重新申请</WoButton>
    </div>

    <!-- 售后申请时效过期提醒弹窗 -->
    <!-- 当用户尝试对过期订单进行售后操作时显示 -->
    <Popup class="popup after-sales-expiration-popup" :style="{ minHeight: '240px' }" safe-area-inset-bottom lock-scroll
           round position="bottom" v-model:visible="afterSalesExpirationPopupShow">
      <!-- 弹窗头部 -->
      <div class="popup-header">
        <p class="title"></p>
        <!-- 关闭弹窗按钮 -->
        <img @click="popupClose" class="close" src="./assets/popupClose.png" alt="" srcset="">
      </div>

      <!-- 弹窗内容区域 -->
      <div class="popup-content">
        <div class="after-sales-expiration-content">
          <!-- 主要提示信息 -->
          <p class="after-sales-expiration-tips">抱歉，订单已过售后申请时效</p>
          <!-- 辅助说明信息 -->
          <p class="after-sales-expiration-sub-tips">商品已超过售后期限，如需售后可联系客服处理</p>
        </div>
      </div>

      <!-- 弹窗操作区域 -->
      <div class="popup-op">
        <!-- 确定按钮，关闭弹窗 -->
        <div class="popup-op-btn" @click="afterSalesExpirationPopupShow = false">
          确定
        </div>
      </div>
    </Popup>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { cancelAfterSales, getAfterSalesExpress, getOrderAfterSalesInfo } from '@api/interface/order.js'
import { getBizCode } from '@utils/curEnv.js'
import OrderAfterSalesDetailSkeleton from './components/OrderAfterSalesDetailSkeleton.vue'

import { closeToast, Popup, showLoadingToast, showToast } from 'vant'
import dayjs from 'dayjs'
import { useAfterSalesStore } from '@store/modules/afterSales.js'
import useClipboard from 'vue-clipboard3'
import { afterSalesProduct } from '@utils/storage.js'
import { useAlert } from '@/composables/index.js'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import {fenToYuan} from "@utils/amount.js";
import {JD_GOODS_CODE} from "@utils/types.js";

// ===================== 售后流程步骤枚举定义 =====================
// 退款流程步骤枚举1 - 标准退款流程
// 包含商家审核和退款完成两个主要步骤
const refundStepEnum1 = [
  {
    id: 1,
    title: '商家审核',
    tipsTitle: '等待商家审核',
    tipsSubTitle: '您已提交退款申请，请耐心等待商家审核，如遇到快递员派件时请拒收，否则可能造成退款失败。'
  },
  {
    id: 6,
    title: '退款完成',
    tipsTitle: '退款完成',
    tipsSubTitle: '您提交的退款已完成，预计3个工作日内退回原支付账户，请注意查收。'
  }
]

// 退款流程步骤枚举2 - 变体退款流程
// 与枚举1类似，但起始步骤ID不同
const refundStepEnum2 = [
  {
    id: 2,
    title: '商家审核',
    tipsTitle: '等待商家审核',
    tipsSubTitle: '您已提交退款申请，请耐心等待商家审核，如遇到快递员派件时请拒收，否则可能造成退款失败。'
  },
  {
    id: 6,
    title: '退款完成',
    tipsTitle: '退款完成',
    tipsSubTitle: '您提交的退款已完成，预计3个工作日内退回原支付账户，请注意查收。'
  }
]

// 退货流程步骤枚举1 - 标准退货流程
// 包含商家审核、寄回商品、商家退款、退款完成四个步骤
const returnStepEnum1 = [
  {
    id: 1,
    title: '商家审核',
    tipsTitle: '等待商家审核',
    tipsSubTitle: '您已提交退款申请，请耐心等待商家审核，如遇到快递员派件时请拒收，否则可能造成退款失败。'
  },
  {
    id: 3,
    title: '寄回商品',
    tipsTitle: '寄回商品',
    tipsSubTitle: '请尽快寄回商品，未与商家协商一致，请勿使用到付或平邮，以免商家拒签货物。'
  },
  {
    id: 5,
    title: '商家退款',
    tipsTitle: '等待商家退款',
    tipsSubTitle: '如果商家收到货并验货无误,将操作退款给您,如果商家拒绝退款请联系客服处理。'
  },
  {
    id: 6,
    title: '退款完成',
    tipsTitle: '退款完成',
    tipsSubTitle: '您提交的退款已完成，预计3个工作日内退回原支付账户，请注意查收。'
  }
]

// 退货流程步骤枚举2 - 变体退货流程
// 与枚举1类似，但商家退款步骤ID不同
const returnStepEnum2 = [
  {
    id: 1,
    title: '商家审核',
    tipsTitle: '等待商家审核',
    tipsSubTitle: '您已提交退款申请，请耐心等待商家审核，如遇到快递员派件时请拒收，否则可能造成退款失败。'
  },
  {
    id: 3,
    title: '寄回商品',
    tipsTitle: '寄回商品',
    tipsSubTitle: '请尽快寄回商品，未与商家协商一致，请勿使用到付或平邮，以免商家拒签货物。'
  },
  {
    id: 4,
    title: '商家退款',
    tipsTitle: '等待商家退款',
    tipsSubTitle: '如果商家收到货并验货无误,将操作退款给您,如果商家拒绝退款请联系客服处理。'
  },
  {
    id: 6,
    title: '退款完成',
    tipsTitle: '退款完成',
    tipsSubTitle: '您提交的退款已完成，预计3个工作日内退回原支付账户，请注意查收。'
  }
]

// 退货流程步骤枚举3 - 另一种变体退货流程
// 起始步骤ID为2，其他步骤与枚举2相同
const returnStepEnum3 = [
  {
    id: 2,
    title: '商家审核',
    tipsTitle: '等待商家审核',
    tipsSubTitle: '您已提交退款申请，请耐心等待商家审核，如遇到快递员派件时请拒收，否则可能造成退款失败。'
  },
  {
    id: 3,
    title: '寄回商品',
    tipsTitle: '寄回商品',
    tipsSubTitle: '请尽快寄回商品，未与商家协商一致，请勿使用到付或平邮，以免商家拒签货物。'
  },
  {
    id: 4,
    title: '商家退款',
    tipsTitle: '等待商家退款',
    tipsSubTitle: '如果商家收到货并验货无误,将操作退款给您,如果商家拒绝退款请联系客服处理。'
  },
  {
    id: 6,
    title: '退款完成',
    tipsTitle: '退款完成',
    tipsSubTitle: '您提交的退款已完成，预计3个工作日内退回原支付账户，请注意查收。'
  }
]

// 退款关闭流程步骤枚举
// 用于处理退款被取消或拒绝的情况
const refundClosedStepEnum = [
  {
    id: 0,
    title: '退款关闭',
    tipsTitle: '退款关闭',
    tipsSubTitle: '因您主动取消退款申请，退款申请已关闭，如问题未解决，可在有效期内再次申请售后。'
  },
  {
    id: 7,
    title: '退款关闭',
    tipsTitle: '退款关闭',
    tipsSubTitle: '商家拒绝退货，有运营人员或者商家填写原因。'
  }
]

// ===================== 路由和状态管理集成 =====================
// 获取路由实例，用于页面跳转和参数获取
const route = useRoute()
const router = useRouter()

// 获取售后状态管理store实例
const afterSalesStore = useAfterSalesStore()

// 获取剪贴板功能，用于复制文本
const { toClipboard } = useClipboard()

// 获取弹窗提示功能
const $alert = useAlert()

// ===================== 售后详情数据管理 =====================
// 售后类型，从路由参数获取（1: 退款, 2: 退货）
const type = ref(route.query.type)

// 售后申请ID，从路由参数获取
const afterSaleId = ref(route.query.afterSaleId)

// 业务代码，用于API请求
const bizCode = ref(getBizCode())

// 数据加载完成状态标识
const dataLoaded = ref(false)

// 售后详情信息，包含订单跟踪和取消信息
const afterSaleDetailInfo = reactive({
  orderTrack: '',
  cancelSupplierAfterSalRec: {}
})

// 售后商品信息
const afterSaleGoodsInfo = ref([])

// ===================== 流程步骤控制 =====================
// 当前流程步骤索引，用于控制步骤进度显示
const stepIndex = ref(1)

// 当前使用的步骤枚举
const stepEnum = ref([])

// ===================== 供应商类型判断 =====================
// 供应商类型标识
const isJD = ref(false)
const isSupplier = ref(false)

// ===================== 物流信息管理 =====================
// 物流信息状态标识
const isLogisticsInfo = ref(false)

// ===================== 弹窗状态控制 =====================
// 售后申请时效过期弹窗显示状态
const afterSalesExpirationPopupShow = ref(false)

// ===================== 供应商售后取消记录判断 =====================
// 判断是否存在供应商售后取消记录
// 用于确定是否有取消售后的相关信息
const isCancelSupplierAfterSalRec = computed(() => {
  return Object.keys(afterSaleDetailInfo.cancelSupplierAfterSalRec).length > 0
})

// 判断是否显示供应商售后取消步骤
// 根据取消记录的结果状态决定是否显示步骤流程
const cancelSupplierAfterSalRecStep = computed(() => {
  if (isCancelSupplierAfterSalRec.value) {
    // 结果状态为'0'时显示步骤
    if (afterSaleDetailInfo.cancelSupplierAfterSalRec.RESULT_STATE === '0') {
      return true
    }
    // 结果状态为'1'时不显示步骤
    if (afterSaleDetailInfo.cancelSupplierAfterSalRec.RESULT_STATE === '1') {
      return false
    }
    // 结果状态为'2'时显示步骤
    if (afterSaleDetailInfo.cancelSupplierAfterSalRec.RESULT_STATE === '2') {
      return true
    }
  }
  return true
})

// 判断是否显示供应商售后取消按钮
// 根据取消记录的结果状态决定是否显示操作按钮
const cancelSupplierAfterSalRecBtn = computed(() => {
  if (isCancelSupplierAfterSalRec.value) {
    // 结果状态为'0'或'1'时不显示按钮
    if (afterSaleDetailInfo.cancelSupplierAfterSalRec.RESULT_STATE === '0') {
      return false
    }
    if (afterSaleDetailInfo.cancelSupplierAfterSalRec.RESULT_STATE === '1') {
      return false
    }
    // 结果状态为'2'时显示按钮
    if (afterSaleDetailInfo.cancelSupplierAfterSalRec.RESULT_STATE === '2') {
      return true
    }
  }
  return true
})

// ===================== 操作按钮显示控制 =====================
// 判断是否显示取消退款按钮
// 综合考虑按钮显示条件、售后类型、状态和供应商类型
const showCancelRefundButton = computed(() => {
  return cancelSupplierAfterSalRecBtn.value &&
    type.value === '1' &&
    afterSaleDetailInfo.afterSaleState !== '0' &&
    afterSaleDetailInfo.afterSaleState !== '7' &&
    afterSaleDetailInfo.afterSaleState !== '6' &&
    !isJD.value && !isSupplier.value
})

// 判断是否显示取消退货按钮
// 与取消退款按钮类似，但针对退货类型
const showCancelReturnButton = computed(() => {
  return cancelSupplierAfterSalRecBtn.value &&
    type.value === '2' &&
    afterSaleDetailInfo.afterSaleState !== '0' &&
    afterSaleDetailInfo.afterSaleState !== '7' &&
    afterSaleDetailInfo.afterSaleState !== '6' &&
    !isJD.value && !isSupplier.value
})

// 判断是否显示重新申请按钮
// 当售后被关闭或拒绝时显示重新申请选项
const showReapplyButton = computed(() => {
  return cancelSupplierAfterSalRecBtn.value &&
    (afterSaleDetailInfo.afterSaleState === '0' ||
      afterSaleDetailInfo.afterSaleState === '7') &&
    afterSaleDetailInfo.afterSaleState !== '6'
})

// ===================== 弹窗控制处理 =====================
// 关闭弹窗的处理函数
// 隐藏售后申请时效过期提醒弹窗
const popupClose = () => {
  afterSalesExpirationPopupShow.value = false
}

// ===================== 客服联系功能 =====================
// 联系客服的处理函数
// 跳转到在线客服页面，提供用户支持服务
const onHotLineClick = async () => {
  window.location.href = 'https://service.unicompayment.com/live800/chatClient/chatbox.jsp?companyID=9061&configID=47&pagereferrer=%e5%95%86%e5%9f%8e&chatfrom=sc&enterurl=sc&sc=sc'
}

// ===================== 物流信息处理 =====================
// 获取售后快递信息的异步函数
// 调用API获取物流跟踪详情
const getExpress = async (applySaleApplyId) => {
  showLoadingToast()
  try {
    const [err, json] = await getAfterSalesExpress(applySaleApplyId)
    closeToast()
    if (!err) return json
    return {}
  } catch (error) {
    logger.error(error)
    closeToast()
    return {}
  }
}

// 快递信息点击处理函数
// 根据类型参数执行不同的操作（获取信息或跳转页面）
const onExpressClick = async (type = 0) => {
  const { id, supplierOrderId } = afterSaleDetailInfo

  if (type === 0) {
    // 类型0：获取并处理快递跟踪信息
    const orderExpress = await getExpress(id)
    const { orderTrack } = orderExpress

    if (Object.keys(orderExpress).length > 0 && orderTrack && orderTrack.length > 0) {
      const filterLastInfo = orderTrack.shift()
      afterSaleDetailInfo.orderTrack = filterLastInfo.context
      isLogisticsInfo.value = true
    } else {
      isLogisticsInfo.value = false
    }
  }

  if (type === 1) {
    // 类型1：跳转到快递详情页面
    router.push({
      path: '/wo-after-sales-express',
      query: {
        applySaleApplyId: id,
        orderId: supplierOrderId
      }
    })
  }
}

// 设置物流单号的处理函数
// 跳转到物流信息填写页面
const setTrackingNumber = () => {
  const { id } = afterSaleDetailInfo
  router.push({
    path: '/wo-after-sales-logic-info',
    query: {
      applySaleApplyId: id
    }
  })
}

const getOrderAfterSalesInfoData = async () => {
  showLoadingToast()
  try {
    const [err, json] = await getOrderAfterSalesInfo(afterSaleId.value, bizCode.value)
    closeToast()
    if (!err) {
      Object.assign(afterSaleDetailInfo, json)
      const afterSaleState = afterSaleDetailInfo.stateProcess.split(',').pop()
      afterSaleDetailInfo.afterSaleState = afterSaleState

      if (type.value === '1' || type.value === '2') {
        if (type.value === '1') {
          if (afterSaleDetailInfo.afterSaleState === '1') {
            stepEnum.value = refundStepEnum1
          } else if (afterSaleDetailInfo.afterSaleState === '2') {
            stepEnum.value = refundStepEnum2
          } else {
            stepEnum.value = refundStepEnum1
          }
        } else {
          if (afterSaleDetailInfo.afterSaleState === '5') {
            stepEnum.value = returnStepEnum1
          } else if (afterSaleDetailInfo.afterSaleState === '4') {
            stepEnum.value = returnStepEnum2
          } else if (afterSaleDetailInfo.afterSaleState === '2') {
            stepEnum.value = returnStepEnum3
          } else {
            console.warn(5645564556456)
            if (afterSaleDetailInfo.returnPolicy) {
              const isNo3List = returnStepEnum1.filter(item => item.id !== 3)
              console.warn(isNo3List)
              stepEnum.value = [...isNo3List, {
                id: 3,
                title: '寄回商品',
                tipsTitle: '寄回商品',
                tipsSubTitle: '请尽快寄回商品，未与商家协商一致，请勿使用到付或平邮，以免商家拒签货物'
              }]
            } else {
              stepEnum.value = returnStepEnum1
            }
          }
        }
        const afterSaleStepCurInfo = stepEnum.value.filter(item => item.id === +json.afterSaleState)[0]
        afterSaleDetailInfo.afterSaleStepCurInfo = afterSaleStepCurInfo
      }

      afterSaleDetailInfo.returnPolicy = json.detail.returnPolicy
      afterSaleDetailInfo.applyRefundSkuNum = json.detail.applyRefundSkuNum
      afterSaleDetailInfo.refundReason = json.detail.refundReason
      afterSaleDetailInfo.applyRefundMoney = fenToYuan(json.detail.applyRefundMoney)
      afterSaleDetailInfo.rejectReasonNum = json.detail.rejectReasonNum
      afterSaleDetailInfo.rejectReason = json.detail.rejectReason
      afterSaleDetailInfo.applyRejectSkuNum = json.detail.applyRejectSkuNum
      afterSaleDetailInfo.refundMoney = fenToYuan(json.detail.refundMoney)
      afterSaleDetailInfo.refundDate = json.detail.refundDate

      if (afterSaleDetailInfo.cancelSupplierAfterSalRec.length > 0) {
        const cancelSupplierAfterSalRec = afterSaleDetailInfo.cancelSupplierAfterSalRec[0]
        if (cancelSupplierAfterSalRec.RESULT_STATE === '0') {
          afterSaleDetailInfo.cancelSupplierAfterSalRec = cancelSupplierAfterSalRec
          stepEnum.value[0].tipsSubTitle = '您已经提交取消申请，等待商家确认。'
        } else if (cancelSupplierAfterSalRec.RESULT_STATE === '1') {
          afterSaleDetailInfo.cancelSupplierAfterSalRec = cancelSupplierAfterSalRec
          if (type.value === '1') {
            stepEnum.value[0].tipsTitle = '退款关闭'
          } else if (type.value === '2') {
            stepEnum.value[0].tipsTitle = '退货关闭'
          }
          stepEnum.value[0].tipsSubTitle = '因您主动取消退款申请，退款申请已关闭。如问题未解决，可在有效期内再次申请售后。'
        } else if (cancelSupplierAfterSalRec.RESULT_STATE === '2') {
          stepEnum.value[0].tipsSubTitle = '取消售后失败，继续按原流程进行，如有疑问请联系在线客服。'
          afterSaleDetailInfo.cancelSupplierAfterSalRec = {}
        }
      }

      if (json.detail.returnAddress) {
        const { recName, recPhone, addrDetail } = json.detail.returnAddress
        const returnAddressInfo = {
          recName,
          recPhone,
          addrDetail: `${addrDetail}`
        }
        afterSaleDetailInfo.returnAddressInfo = returnAddressInfo
      } else {
        afterSaleDetailInfo.returnAddressInfo = ''
      }

      // 获取最后一个售后状态，展现在头部，直接拼接到前端数据结构
      afterSaleDetailInfo.lastAfterSalesState = json.afterSaleState[json.afterSaleState.length - 1]
      afterSaleGoodsInfo.value = json.skuInfo
      const params = []
      if (afterSaleGoodsInfo.value.param) params.push(afterSaleGoodsInfo.value.param)
      if (afterSaleGoodsInfo.value.param1) params.push(afterSaleGoodsInfo.value.param1)
      if (afterSaleGoodsInfo.value.param2) params.push(afterSaleGoodsInfo.value.param2)
      if (afterSaleGoodsInfo.value.param3) params.push(afterSaleGoodsInfo.value.param3)
      if (afterSaleGoodsInfo.value.param4) params.push(afterSaleGoodsInfo.value.param4)
      afterSaleGoodsInfo.value.spec = params.join(' ')

      afterSaleGoodsInfo.value.detailImageUrl = afterSaleGoodsInfo.value.detailImageUrl[0]

      stepIndex.value = json.stateProcess.split(',').length
      if (json.afterSaleState === '0' || json.afterSaleState === '7') {
        const stepEnum = refundClosedStepEnum
        const afterSaleStepCurInfo = stepEnum.filter(item => item.id === +json.afterSaleState)[0]
        if (json.afterSaleState === '7' && afterSaleDetailInfo.exmReDes) {
          afterSaleStepCurInfo.tipsSubTitle = afterSaleDetailInfo.exmReDes
        } else if (json.afterSaleState === '7' && !afterSaleDetailInfo.exmReDes) {
          afterSaleStepCurInfo.tipsSubTitle = ''
        }
        afterSaleDetailInfo.afterSaleStepCurInfo = afterSaleStepCurInfo
      }

      isJD.value = afterSaleDetailInfo.supplierCode.indexOf(JD_GOODS_CODE) > -1
      const codesToCheck = ['zst', 'hzamdzswyxgs', 'bjhbkjyxgs', 'jd_yg', 'jd_wbf']
      isSupplier.value = codesToCheck.some(code => afterSaleDetailInfo.supplierCode.indexOf(code) !== -1)
      dataLoaded.value = true
    }

    if (afterSaleDetailInfo.afterSaleState === '5' || afterSaleDetailInfo.afterSaleState === '4') {
      onExpressClick()
    }
  } catch (error) {
    logger.error(error)
    closeToast()
    // 即使出错也要设置加载完成状态，避免一直显示骨架屏
    dataLoaded.value = true
  }
}

const copyText = async (text) => {
  try {
    await toClipboard(text)
    showToast('复制成功')
  } catch (error) {
    logger.error(error)
    showToast('复制失败')
  }
}

const cancelReturn = () => {
  const cancelOrderFn = async () => {
    showLoadingToast()
    try {
      const [err] = await cancelAfterSales(afterSaleId.value, bizCode.value)
      closeToast()
      if (!err) {
        await getOrderAfterSalesInfoData()
      } else {
        showToast(err.msg)
      }
    } catch (error) {
      logger.error(error)
      closeToast()
    }
  }

  // 使用Vant的Dialog替代原来的$alert
  $alert({
    title: '',
    message: '您确定要取消本次退货吗？如超出售后服务期，将无法再次发起售后申请。',
    confirmButtonText: '取消退货',
    cancelButtonText: '我再想想',
    showCancelButton: true,
    closeOnClickOverlay: true,
    onConfirmCallback: cancelOrderFn
  })
}

const cancelRefund = () => {
  const cancelOrderFn = async () => {
    showLoadingToast()
    try {
      const [err] = await cancelAfterSales(afterSaleId.value, bizCode.value)
      closeToast()
      if (!err) {
        await getOrderAfterSalesInfo()
      } else {
        showToast(err.msg)
      }
    } catch (error) {
      logger.error(error)
      closeToast()
    }
  }

  $alert({
    title: '',
    message: '你确定要取消本次退款吗？如超出售后服务期，将无法再次发起退款申请。',
    confirmButtonText: '取消退款',
    cancelButtonText: '我再想想',
    showCancelButton: true,
    closeOnClickOverlay: true,
    onConfirmCallback: cancelOrderFn
  })
}

const reapply = async () => {
  const { orderDate } = afterSaleDetailInfo
  // 订单时间
  const expDate = dayjs(orderDate).add(15, 'day')
  // 当前时间
  const now = dayjs()
  // 当前订单是否在有效期内
  const isExpires = expDate > now
  if (!isExpires) {
    afterSalesExpirationPopupShow.value = true
    return
  }

  const { applyType, afterSaleState, bizOrderId, bizCode } = afterSaleDetailInfo
  const { orderState } = afterSalesProduct.get()

  if (applyType === '1') {
    if (orderState === '1' || orderState === '3') {
      // 将售后相关信息保存到Vuex中
      afterSalesStore.updateAfterSalesInfo({
        applyType,
        afterSaleState,
        bizOrderId,
        bizCode,
        orderState
      })

      router.go(-1)
    } else {
      router.replace({
        path: '/wo-after-sales-entry',
        query: {
          orderState
        }
      })
    }
  } else if (applyType === '2') {
    router.replace({
      path: '/wo-after-sales-entry',
      query: {
        orderState
      }
    })
  }
}

// 生命周期钩子
onMounted(() => {
  getOrderAfterSalesInfoData()
})
</script>

<style scoped lang="less">
.order-after-sales-detail {
  background: rgba(247, 247, 247, 0.80);
  padding-bottom: 50px;
  min-height: 100vh;
  box-sizing: border-box;

  .module-divider {
    width: 100%;
    height: 10px;
    background: rgba(247, 247, 247, 0.80);
  }

  .after-sales-process {
    box-sizing: border-box;
    padding: 17px;
    background-color: #FFFFFF;

    .steps {
      display: flex;
      align-items: center;
      justify-content: center;

      .steps-item {
        position: relative;
        width: 25%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .item-top {
          text-align: center;
          width: 100%;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;

          .node-icon {
            position: relative;
            width: 18px;
            height: 18px;
            z-index: 60;
          }

          &:before {
            position: absolute;
            top: 50%;
            left: 0;
            content: '';
            width: 50%;
            height: 4px;
            background-color: #DFDFDF;
            transform: translateY(-50%);
          }

          &:after {
            position: absolute;
            top: 50%;
            right: 0;
            content: '';
            width: 50%;
            height: 4px;
            background-color: #DFDFDF;
            transform: translateY(-50%);
          }
        }

        .item-bottom {
          font-size: 0;
          margin-top: 8px;

          .node-name {
            margin-top: 10px;
            font-size: 13px;
            color: #171E24;
            line-height: 1.5;
            font-weight: 400;
          }
        }

        &:first-child .item-top:before {
          display: none;
        }

        &:last-child .item-top:after {
          display: none;
        }

        &.active .item-top:before {
          background-color: #F97316;
        }

        &.active .item-top:after {
          background-color: #F97316;
        }
      }
    }

    .content {
      margin-top: 12px;

      .process-title {
        font-size: 18px;
        color: #171E24;
        line-height: 1.5;
        font-weight: 700;
      }

      .process-tips {
        margin-top: 6px;
        font-size: 15px;
        color: #4A5568;
        line-height: 1.5;
        font-weight: 400;
      }
    }
  }

  .logistics-information {
    padding: 17px;
    background-color: #FFFFFF;

    .business {
      display: flex;
      justify-content: space-between;

      .business-address {
        flex: 1;

        .base {
          font-size: 16px;
          color: #171E24;
          line-height: 1.5;
          font-weight: 400;

          .phone {
            margin-left: 5px;
            color: #4A5568;
          }
        }

        .address {
          margin-top: 6px;
          font-size: 12px;
          color: #718096;
          line-height: 1.5;
          font-weight: 400;
        }

        .return-policy {
          margin-top: 6px;
          font-size: 12px;
          color: #718096;
          line-height: 1.5;
          font-weight: 400;
        }
      }

      .copy {
        font-size: 16px;
        margin-left: 5px;
        color: #F97316;
      }
    }

    .mine-logistics {
      margin-top: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .base {
        .title {
          font-size: 16px;
          color: #171E24;
          line-height: 1;
          font-weight: 400;
        }

        .tips {
          margin-top: 6px;
          font-size: 12px;
          color: #718096;
          line-height: 1;
          font-weight: 400;
        }
      }

      .input-logistics-btn {
        font-size: 12px;
        color: #718096;
        text-align: center;
        line-height: 30px;
        font-weight: 400;
        width: 80px;
        height: 30px;
        border: 1px solid #718096;
        border-radius: 15px;
      }
    }
  }

  .logistics-compete {
    box-sizing: border-box;
    background-color: #FFFFFF;
    padding: 0 17px;

    .wo-cell {
      width: 100%;
      min-height: 55px;
      display: flex;
      justify-content: space-between;
      padding: 15px 0;

      &.is-center {
        align-items: center;
      }

      &.is-border {
        border-bottom: 1px solid rgba(227, 227, 227, 1);
      }

      .is-require {
        position: relative;

        &:after {
          content: '*';
          position: absolute;
          top: -3px;
          right: -10px;
          color: #EF4444;
          font-size: 25px;
        }
      }

      .is-vertical {
        flex-direction: column;
      }

      .cell-left {
        min-width: 65px;
        margin-right: 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .left-title {
          font-size: 16px;
          color: #171E24;
          line-height: 1;
          font-weight: 400;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .cell-right {
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .right-title {
          font-size: 15px;
          color: #718096;
          line-height: 1;
          font-weight: 400;
        }

        .right-arrow {
          margin-left: 5px;
          width: 15px;
          height: 15px;
        }

        .customize-content {
          .content {
            font-size: 15px;
            color: #171E24;
            text-align: right;
            line-height: 1.2;
            font-weight: 400;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            white-space: normal;
          }

          .tips {
            margin-top: 8px;
            font-size: 12px;
            color: #718096;
            text-align: right;
            line-height: 1.2;
            font-weight: 400;
          }
        }

        .goods-price {
          width: 100%;
          text-align: right;
          display: flex;
          align-items: flex-end;
          justify-content: flex-end;

          .num {
            margin-right: 8px;
            font-size: 18px;
            color: #F97316;
            line-height: 1;
            font-weight: 400;

            .integer {
              font-weight: 600;
            }

            .decimal {
              font-size: 15px;
              font-weight: 500;
            }

            &:before {
              content: '￥';
              font-size: 13px;
            }
          }

          .edit {
            width: 14px;
            height: 14px;
          }
        }

        .goods-price-tips {
          margin-top: 8px;
          font-size: 12px;
          color: #718096;
          text-align: right;
          line-height: 1;
          font-weight: 400;
        }
      }
    }
  }

  .after-sales-info {
    box-sizing: border-box;
    padding: 17px;
    background-color: #FFFFFF;

    .info-title {
      font-size: 16px;
      color: #171E24;
      line-height: 1.5;
      font-weight: 400;
      margin-bottom: 10px;
    }

    .after-sales-goods {
      width: 100%;
      box-sizing: border-box;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      background: #fff;

      .goods-img {
        width: 70px;
        height: 70px;
        margin-right: 15px;
      }

      .goods-info {
        flex: 1;
        overflow: hidden;

        .goods-name {
          margin-bottom: 10px;
          font-size: 15px;
          color: #171E24;
          line-height: 1.5;
          font-weight: 600;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .goods-spec {
          font-size: 12px;
          color: #4A5568;
          line-height: 1.5;
          font-weight: 400;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .info-content {
      .info-cell {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .cell-left {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          font-size: 14px;
          color: #4A5568;
          line-height: 1.5;
          font-weight: 400;
          min-width: 70px;
          margin-right: 5px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .cell-right {
          flex: 1;
          font-size: 14px;
          color: #171E24;
          text-align: right;
          line-height: 1.5;
          font-weight: 400;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .copy {
            margin-left: 5px;
            color: #F97316;
          }
        }
      }
    }
  }

  .op {
    position: fixed;
    bottom: 0;
    left: 0;
    box-sizing: border-box;
    padding: 12px 17px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #FFFFFF;
    gap: 10px;
    .contact-online-customer {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #F97316;
      font-size: 14px;
      font-weight: 400;

      i {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-right: 5px;
        background-image: url("./assets/customer-service.png");
        background-repeat: no-repeat;
        background-size: contain;
      }
    }
  }
}

.popup {
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .popup-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .title {
      flex: 1;
      font-size: 17px;
      color: #171E24;
      text-align: center;
      line-height: 1;
      font-weight: 400;
    }

    .close {
      width: 14px;
      height: 14px;
    }
  }

  .popup-content {
    margin-bottom: 50px;

    .after-sales-expiration-content {
      .after-sales-expiration-tips {
        font-size: 19px;
        color: #171E24;
        text-align: center;
        font-weight: 700;
        margin-bottom: 15px;
      }

      .after-sales-expiration-sub-tips {
        margin-top: 10px;
        font-size: 13px;
        color: #4A5568;
        text-align: center;
        font-weight: 400;
      }
    }
  }

  .popup-op {
    width: 100%;
    height: 35px;
    margin-top: 20px;

    .popup-op-btn {
      background-image: var(--wo-biz-theme-gradient-1);
      border-radius: 49px;
      font-size: 17px;
      color: #FFFFFF;
      font-weight: 400;
      width: 100%;
      height: 35px;
      text-align: center;
      line-height: 35px;
    }
  }
}
</style>
