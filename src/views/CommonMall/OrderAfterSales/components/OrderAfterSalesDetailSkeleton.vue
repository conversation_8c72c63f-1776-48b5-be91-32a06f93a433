<!--
/**
 * 订单售后详情页面骨架屏组件
 *
 * 主要功能：
 * 1. 为OrderAfterSalesDetail.vue提供加载时的骨架屏效果
 * 2. 模拟售后流程进度区域的布局和尺寸
 * 3. 模拟物流信息区域的布局和尺寸
 * 4. 模拟售后信息详情区域的布局和尺寸
 * 5. 模拟操作按钮区域的布局和尺寸
 * 6. 提供流畅的骨架屏动画效果
 *
 * 技术特点：
 * - 使用CSS动画实现骨架屏闪烁效果
 * - 精确匹配原页面的布局尺寸和间距
 * - 模块化设计，各区域独立展示
 * - 响应式设计，适配不同屏幕尺寸
 * - 轻量级实现，不依赖外部组件
 */
-->

<template>
  <!-- 售后详情骨架屏主容器 -->
  <div class="order-after-sales-detail-skeleton">
    <!-- 售后流程进度骨架屏区域 -->
    <div class="after-sales-process-skeleton">
      <!-- 流程步骤指示器骨架屏 -->
      <div class="steps-skeleton">
        <!-- 步骤项骨架屏循环 -->
        <div class="steps-item-skeleton" v-for="n in 4" :key="n">
          <!-- 步骤图标区域骨架屏 -->
          <div class="item-top-skeleton">
            <div class="node-icon-skeleton skeleton-item"></div>
          </div>
          <!-- 步骤名称区域骨架屏 -->
          <div class="item-bottom-skeleton">
            <div class="node-name-skeleton skeleton-item"></div>
          </div>
        </div>
      </div>
      <!-- 流程内容提示区域骨架屏 -->
      <div class="content-skeleton">
        <!-- 当前步骤标题骨架屏 -->
        <div class="process-title-skeleton skeleton-item"></div>
        <!-- 当前步骤详细说明骨架屏 -->
        <div class="process-tips-skeleton skeleton-item"></div>
      </div>
    </div>
    
    <!-- 模块分隔线 -->
    <div class="module-divider"></div>
    
    <!-- 物流信息骨架屏区域 -->
    <div class="logistics-information-skeleton">
      <!-- 商家收货地址信息骨架屏 -->
      <div class="business-skeleton">
        <div class="business-address-skeleton">
          <!-- 收货人姓名和电话骨架屏 -->
          <div class="base-skeleton">
            <div class="address-info-skeleton skeleton-item"></div>
          </div>
          <!-- 详细收货地址骨架屏 -->
          <div class="address-skeleton skeleton-item"></div>
          <!-- 退货政策说明骨架屏 -->
          <div class="return-policy-skeleton skeleton-item"></div>
        </div>
        <!-- 复制地址信息按钮骨架屏 -->
        <div class="copy-skeleton skeleton-item"></div>
      </div>
      <!-- 用户物流信息填写区域骨架屏 -->
      <div class="mine-logistics-skeleton">
        <div class="base-skeleton">
          <div class="title-skeleton skeleton-item"></div>
          <div class="tips-skeleton skeleton-item"></div>
        </div>
        <!-- 填写物流单号按钮骨架屏 -->
        <div class="input-logistics-btn-skeleton skeleton-item"></div>
      </div>
    </div>
    
    <!-- 模块分隔线 -->
    <div class="module-divider"></div>

    <!-- 售后信息详情骨架屏区域 -->
    <div class="after-sales-info-skeleton">
      <!-- 信息标题骨架屏 -->
      <div class="info-title-skeleton skeleton-item"></div>

      <!-- 售后商品信息展示骨架屏 -->
      <div class="after-sales-goods-skeleton">
        <!-- 商品图片骨架屏 -->
        <div class="goods-img-skeleton skeleton-item"></div>
        <!-- 商品基本信息骨架屏 -->
        <div class="goods-info-skeleton">
          <!-- 商品名称骨架屏 -->
          <div class="goods-name-skeleton skeleton-item"></div>
          <!-- 商品规格骨架屏 -->
          <div class="goods-spec-skeleton skeleton-item"></div>
        </div>
      </div>

      <!-- 售后详细信息列表骨架屏 -->
      <div class="info-content-skeleton">
        <!-- 信息单元格骨架屏循环 -->
        <div class="info-cell-skeleton" v-for="n in 6" :key="n">
          <div class="cell-left-skeleton skeleton-item"></div>
          <div class="cell-right-skeleton skeleton-item"></div>
        </div>
      </div>
    </div>
    
    <!-- 操作按钮区域骨架屏 -->
    <div class="op-skeleton">
      <!-- 按钮骨架屏 -->
      <div class="button-skeleton skeleton-item" v-for="n in 2" :key="n"></div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件无需响应式逻辑，仅提供静态展示效果
</script>

<style scoped lang="less">
// 骨架屏动画效果
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// 骨架屏基础样式
.skeleton-item {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.order-after-sales-detail-skeleton {
  min-height: 100vh;
  background-color: #FFFFFF;
}

.module-divider {
  width: 100%;
  height: 10px;
  background-color: #F8F9FA;
}

// 售后流程进度骨架屏样式
.after-sales-process-skeleton {
  padding: 20px 17px;
  background-color: #FFFFFF;
}

.steps-skeleton {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.steps-item-skeleton {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.item-top-skeleton {
  margin-bottom: 8px;
}

.node-icon-skeleton {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.node-name-skeleton {
  width: 60px;
  height: 14px;
}

.content-skeleton {
  text-align: center;
}

.process-title-skeleton {
  height: 20px;
  width: 40%;
  margin: 0 auto 10px;
}

.process-tips-skeleton {
  height: 16px;
  width: 80%;
  margin: 0 auto;
}

// 物流信息骨架屏样式
.logistics-information-skeleton {
  padding: 17px;
  background-color: #FFFFFF;
}

.business-skeleton {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #F8F9FA;
  border-radius: 8px;
}

.business-address-skeleton {
  flex: 1;
}

.base-skeleton {
  margin-bottom: 8px;
}

.address-info-skeleton {
  height: 18px;
  width: 70%;
  margin-bottom: 8px;
}

.address-skeleton {
  height: 16px;
  width: 90%;
  margin-bottom: 8px;
}

.return-policy-skeleton {
  height: 14px;
  width: 60%;
}

.copy-skeleton {
  width: 40px;
  height: 30px;
  margin-left: 10px;
}

.mine-logistics-skeleton {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #F8F9FA;
  border-radius: 8px;
}

.title-skeleton {
  height: 18px;
  width: 60px;
  margin-bottom: 5px;
}

.tips-skeleton {
  height: 14px;
  width: 80px;
}

.input-logistics-btn-skeleton {
  width: 60px;
  height: 32px;
}

// 售后信息详情骨架屏样式
.after-sales-info-skeleton {
  padding: 17px;
  background-color: #FFFFFF;
}

.info-title-skeleton {
  height: 20px;
  width: 80px;
  margin-bottom: 15px;
}

.after-sales-goods-skeleton {
  display: flex;
  margin-bottom: 20px;
}

.goods-img-skeleton {
  width: 75px;
  height: 75px;
  margin-right: 15px;
  border-radius: 4px;
}

.goods-info-skeleton {
  flex: 1;
}

.goods-name-skeleton {
  height: 18px;
  width: 80%;
  margin-bottom: 8px;
}

.goods-spec-skeleton {
  height: 14px;
  width: 60%;
}

.info-content-skeleton {
  border-top: 1px solid #E2E8EE;
}

.info-cell-skeleton {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #E2E8EE;

  &:last-child {
    border-bottom: none;
  }
}

.cell-left-skeleton {
  height: 16px;
  width: 80px;
}

.cell-right-skeleton {
  height: 16px;
  width: 120px;
}

// 操作按钮骨架屏样式
.op-skeleton {
  display: flex;
  gap: 10px;
  padding: 20px 17px;
  background-color: #FFFFFF;
}

.button-skeleton {
  flex: 1;
  height: 44px;
  border-radius: 6px;
}
</style>