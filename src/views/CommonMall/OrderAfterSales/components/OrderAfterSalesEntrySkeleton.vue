<!--
/**
 * 订单售后入口页面骨架屏组件
 *
 * 主要功能：
 * 1. 为OrderAfterSalesEntry.vue提供加载时的骨架屏效果
 * 2. 模拟商品信息展示区域的布局和尺寸
 * 3. 模拟售后服务选项区域的布局和尺寸
 * 4. 提供流畅的骨架屏动画效果
 *
 * 技术特点：
 * - 使用CSS动画实现骨架屏闪烁效果
 * - 精确匹配原页面的布局尺寸和间距
 * - 响应式设计，适配不同屏幕尺寸
 * - 轻量级实现，不依赖外部组件
 */
-->

<template>
  <!-- 售后入口骨架屏主容器 -->
  <div class="after-sales-entry-skeleton">
    <!-- 商品信息骨架屏区域 -->
    <section class="product-info-skeleton">
      <!-- 商品图片骨架屏 -->
      <div class="product-info-skeleton__image skeleton-item"></div>
      <!-- 商品详细信息骨架屏 -->
      <div class="product-info-skeleton__details">
        <!-- 商品名称骨架屏 -->
        <div class="product-info-skeleton__name skeleton-item"></div>
        <!-- 商品规格骨架屏 -->
        <div class="product-info-skeleton__spec skeleton-item"></div>
      </div>
    </section>

    <!-- 区域分隔线 -->
    <div class="section-divider"></div>

    <!-- 售后服务选项骨架屏区域 -->
    <section class="service-options-skeleton">
      <!-- 退款服务选项骨架屏 -->
      <div class="service-option-skeleton">
        <!-- 选项内容区域骨架屏 -->
        <div class="service-option-skeleton__content">
          <!-- 服务类型图标骨架屏 -->
          <div class="service-option-skeleton__icon skeleton-item"></div>
          <!-- 服务信息详情骨架屏 -->
          <div class="service-option-skeleton__info">
            <!-- 服务标题骨架屏 -->
            <div class="service-option-skeleton__title skeleton-item"></div>
            <!-- 服务描述骨架屏 -->
            <div class="service-option-skeleton__description skeleton-item"></div>
          </div>
        </div>
        <!-- 进入箭头图标骨架屏 -->
        <div class="service-option-skeleton__arrow skeleton-item"></div>
      </div>

      <!-- 退货退款服务选项骨架屏 -->
      <div class="service-option-skeleton">
        <!-- 选项内容区域骨架屏 -->
        <div class="service-option-skeleton__content">
          <!-- 服务类型图标骨架屏 -->
          <div class="service-option-skeleton__icon skeleton-item"></div>
          <!-- 服务信息详情骨架屏 -->
          <div class="service-option-skeleton__info">
            <!-- 服务标题骨架屏 -->
            <div class="service-option-skeleton__title skeleton-item"></div>
            <!-- 服务描述骨架屏 -->
            <div class="service-option-skeleton__description skeleton-item"></div>
          </div>
        </div>
        <!-- 进入箭头图标骨架屏 -->
        <div class="service-option-skeleton__arrow skeleton-item"></div>
      </div>
    </section>
  </div>
</template>

<script setup>
// 骨架屏组件无需响应式逻辑，仅提供静态展示效果
</script>

<style scoped lang="less">
// 骨架屏动画效果
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// 骨架屏基础样式
.skeleton-item {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.after-sales-entry-skeleton {
  min-height: 100vh;
  background-color: #FFFFFF;
}

.section-divider {
  width: 100%;
  height: 10px;
  background-color: #F8F9FA;
}

// 商品信息骨架屏样式
.product-info-skeleton {
  display: flex;
  padding: 10px 17px;
  background-color: #FFFFFF;

  &__image {
    width: 75px;
    height: 75px;
    margin-right: 15px;
    border-radius: 4px;
    flex-shrink: 0;
  }

  &__details {
    flex: 1;
    overflow: hidden;
  }

  &__name {
    height: 20px;
    margin: 0 0 10px 0;
    width: 80%;
  }

  &__spec {
    height: 16px;
    margin: 0;
    width: 60%;
  }
}

// 服务选项骨架屏样式
.service-options-skeleton {
  padding: 0 17px;
  background-color: #FFFFFF;
}

.service-option-skeleton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 17px 0;
  border-bottom: 1px solid #E2E8EE;

  &:last-child {
    border-bottom: none;
  }

  &__content {
    display: flex;
    align-items: flex-start;
    flex: 1;
    margin-right: 5px;
  }

  &__icon {
    width: 22px;
    height: 22px;
    margin-right: 5px;
    flex-shrink: 0;
  }

  &__info {
    flex: 1;
  }

  &__title {
    height: 20px;
    margin: 0 0 8px 0;
    width: 30%;
  }

  &__description {
    height: 16px;
    margin: 0;
    width: 85%;
  }

  &__arrow {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }
}
</style>