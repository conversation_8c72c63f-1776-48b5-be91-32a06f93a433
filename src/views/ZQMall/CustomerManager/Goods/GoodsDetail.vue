<!--
/**
 * 商品详情页面组件
 *
 * 主要功能：
 * 1. 商品信息展示 - 显示商品图片轮播、基础信息、价格等核心商品数据
 * 2. 规格选择管理 - 支持多规格商品的规格选择，实时更新价格和库存
 * 3. 购买流程控制 - 处理加入购物车和立即购买的完整业务流程
 * 4. 用户权限验证 - 检查用户登录状态、白名单权限、区域销售限制
 * 5. 地址配送管理 - 根据用户地址计算配送信息和物流预测
 * 6. 营销活动展示 - 显示商品相关的营销活动和优惠信息
 * 7. 库存状态监控 - 实时检查商品库存状态和销售状态
 * 8. 分享功能支持 - 支持商品分享到微信等社交平台
 *
 * 技术特性：
 * - 使用 Composition API 进行状态管理和逻辑复用
 * - 集成 Pinia 状态管理，管理用户信息和购物车数据
 * - 支持骨架屏加载，提升用户体验
 * - 实现防抖优化，避免频繁的接口调用
 * - 支持懒加载，优化页面性能
 * - 集成地址选择和配送信息查询
 * - 支持京东商品的特殊处理逻辑
 *
 * 使用场景：
 * - 商城商品详情页面展示
 * - 支持 B2B 和 B2C 不同业务模式
 * - 适配移动端和桌面端显示
 * - 支持多供应商商品展示
 */
-->
<template>
  <div class="goods-detail">
    <!-- 页面加载状态：显示骨架屏，提升用户体验 -->
    <GoodsDetailSkeleton v-if="isLoading" />

    <!-- 商品详情主要内容区域 -->
    <div v-else class="goods-content">
      <!-- 商品图片轮播区域：支持图片预览和视频播放 -->
      <section class="image-section">
        <GoodsSwiper
          :imageList="goodsMediaList"
          :loop="true"
          :autoplay="true"
          mode="square"
          @image-click="handleImagePreview"
          @slide-change="handleSlideChange"
        />
      </section>

      <!-- 商品基础信息展示：名称、价格、描述等核心信息 -->
      <GoodsBasicInfo :goods-info="goodsInfo" />

      <!-- 商品规格选择区域：支持多规格选择和商品编码显示 -->
      <div class="spec-section">
        <div class="spec-header">
          <span class="spec-title">规格</span>
          <!-- 商品编码显示和复制功能 -->
          <div class="goods-code" v-if="currentSku.supplierSkuId">
            <span class="code-label">商品编码：</span>
            <span class="code-value">{{ currentSku.supplierSkuId }}</span>
            <img
              src="@/components/GoodsDetailCommon/assets/copy.png"
              alt="复制"
              class="copy-icon"
              width="16"
              height="16"
              @click="handleCopyCode"
            />
          </div>
        </div>

        <!-- 规格选项列表：动态渲染所有可选规格 -->
        <div class="spec-options">
          <div class="radio-wrapper" v-for="(specs, groupIndex) in displaySpecsList" :key="groupIndex">
            <!-- 规格组标题：如颜色、尺寸等 -->
            <div class="spec-group-title" v-if="specs.length > 0 && hasSpecs">
              {{ getSpecGroupName(groupIndex) }}
            </div>
            <!-- 规格选项按钮：支持选中状态和禁用状态 -->
            <button
              v-for="(spec, specIndex) in specs"
              :key="specIndex"
              :class="{
                active: specOptions.curSpecs.indexOf(spec) >= 0,
                disabled: specOptions.curDisabledSpecs.indexOf(spec) >= 0
              }"
              @click="selectSpec(spec)"
            >
              {{ removeSpecPrefix(spec) }}
            </button>
          </div>
        </div>
      </div>

      <!-- 底部操作栏占位符：为固定底部操作栏预留空间 -->
      <WoActionBarPlaceholder />

      <!-- 底部操作栏：返回首页按钮 -->
      <WoActionBar>
        <WoButton type="primary" block size="xlarge" @click="handleGoToGoodsList">
          返回首页
        </WoButton>
      </WoActionBar>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@store/modules/user.js'
import { useNewCartStore } from '@store/modules/newCart.js'
import {
  productIntroduction,
  queryPredictSkuPromise,
  checkSkuSale,
  isWhiteUserLimitCheck,
  getLimitAreaList,
  getActiveList
} from '@api/interface/goods.js'
import { jdAddressCheck } from '@api/index.js'
import GoodsSwiper from '@components/Common/GoodsSwiper.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import GoodsDetailSkeleton from '@components/GoodsDetailCommon/GoodsDetailSkeleton.vue'
import GoodsBasicInfo from '@components/GoodsDetailCommon/GoodsBasicInfo.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import { getBizCode } from '@utils/curEnv.js'
import { debounce } from 'es-toolkit'
import { closeToast, showLoadingToast, showToast } from 'vant'
import useClipboard from 'vue-clipboard3'
import {removeSpecPrefix, useAlert, useGoodsDetail} from '@/composables/index.js'
import { getDefaultShareUrl, shareData } from '@utils/share.js'
import { setWeiXinShareData } from 'commonkit'
import {JD_GOODS_CODE} from "@utils/types.js";
import logger from "@utils/logger.js";

// ===================== 工具函数和组合式函数初始化 =======================
const $alert = useAlert()
const { toClipboard } = useClipboard()

// ===================== 路由和状态管理初始化 =======================
const route = useRoute()
const router = useRouter()
const goodsId = route.params.goodsId
const skuId = route.params.skuId
const userStore = useUserStore()
const newCartStore = useNewCartStore()

// ===================== 商品详情 Hook 初始化 =======================
// 使用商品详情 hook，管理商品基础数据和规格选择逻辑
const {
  spu,
  curSpecs,
  curSkuId,
  querySpu,
  querySku,
  querySpecsList,
  setSpecs,
  queryCurSpecs,
  queryDisabledSpecs,
} = useGoodsDetail(goodsId, skuId)

// ===================== 页面加载状态管理 =======================
// 页面整体加载状态，控制骨架屏显示
const isLoading = ref(true)
// 内容加载完成状态，控制内容区域显示
const contentLoaded = ref(false)
// 详情页错误状态，用于错误页面显示
const detailErr = ref(false)
// 数据获取完成状态，标识是否已获取到商品数据
const isDataGet = ref(false)
// 初始加载完成标识，用于控制某些只在初始化后执行的逻辑
const initialLoadComplete = ref(false)
// URL更新标识，防止重复更新URL中的skuId
const hasUpdatedUrlForSku = ref(false)

// ===================== 商品基础信息 =======================
// 商品类型判断，是否为京东商品
const isJD = ref(false)
// 用户选择的商品数量
const goodsNum = ref(1)
// 图片预览当前索引
const previewImageIndex = ref(0)

// ===================== 商品详情数据 =======================
// 商品介绍详情数据
const productIntroductionData = ref('')
// 物流服务信息数据
const logisticsServicesInfo = ref({
  logisticsType: 1,
  returnRuleStr: '',
  predictContent: '预计48小时之内发货',
  isJD: false
})

// ===================== 商品限制和状态 =======================
// 白名单用户是否限购，true没有限购，false有限购
const limitState = ref(true)
// 所选区域暂不支持销售，true没有区域限制，false有区域限制
const regionalSalesState = ref(true)

// ===================== 营销活动数据 =======================
// 营销位类型1数据（如满减活动）
const marketTemplatesType1 = ref([])
// 营销位类型4数据（如电子券）
const marketTemplatesType4 = ref([])
// 减免价格信息
const reducePrice = ref(0)

// ===================== 组件引用 =======================
// 规格选择组件引用
const specSelectionRef = ref(null)
// 懒加载商品介绍组件引用
const lazyGoodsIntroduceRef = ref(null)
// 物流服务显示控制
const isShowLogisticsServices = ref(true)

// ===================== 计算属性 - 商品限制相关 =======================
// 起购逻辑计算，处理最低购买数量限制
const lowestBuyObj = computed(() => {
  const lowestBuyValue = currentSku.value?.lowestBuy
  const isLowestBuy = lowestBuyValue ? parseInt(lowestBuyValue, 10) > 1 : false
  const lowestBuyNum = lowestBuyValue ? parseInt(lowestBuyValue, 10) : 1
  const lowestBuyText = lowestBuyValue ? `${lowestBuyNum}件起购` : ''

  return {
    isLowestBuy,
    lowestBuyNum,
    lowestBuyText
  }
})

// ===================== 工具函数 - 数据更新相关 =======================
// 防抖的异步数据更新函数，避免频繁请求京东商品信息
const debouncedUpdateGoodsInfo = debounce(async () => {
  const sku = querySku()
  if (sku && sku.supplierCode && sku.supplierCode.indexOf(JD_GOODS_CODE) > -1) {
    try {
      await Promise.all([
        queryPredictSku(),
        getProductIntroduction()
      ])
    } catch (error) {
      console.error('更新商品信息失败:', error)
    }
  }
}, 300)

// ===================== 工具函数 - 滚动位置管理 =======================
// 保存的滚动位置
let savedScrollPosition = 0

// 保存当前页面滚动位置
const saveScrollPosition = () => {
  savedScrollPosition = window.scrollY || document.documentElement.scrollTop
}

// ===================== 计算属性 - 商品基础数据 =======================
// 当前选中的SKU数据
const currentSku = computed(() => {
  return querySku() || {}
})

// 商品基础信息计算，包含名称、价格、图片等
const goodsInfo = computed(() => {
  const sku = currentSku.value
  if (!sku || !spu.value) {
    return {
      name: '',
      price: 0,
      originalPrice: 0,
      imageUrl: ''
    }
  }
  return {
    name: sku.name || '',
    price: sku.price || 0,
    originalPrice: sku.originalPrice || 0,
    imageUrl: sku.listImageUrl || ''
  }
})

// ===================== 计算属性 - 媒体资源相关 =======================
// 商品媒体列表，处理商品详情图片数据
const goodsMediaList = computed(() => {
  const sku = currentSku.value
  if (!sku || !sku.detailImageUrl) {
    return []
  }
  return sku.detailImageUrl.map(url => ({
    type: 'image',
    url,
    alt: '商品图片'
  }))
})

// ===================== 计算属性 - 规格选择相关 =======================
// 规格选项数据计算，整合规格列表、当前选择和禁用状态
const specOptions = computed(() => {
  // 获取规格数据，参考 GoodsChoose.vue 的逻辑
  const specsList = querySpecsList()
  const curSpecs = queryCurSpecs ? queryCurSpecs() : []
  const disabledSpecs = queryDisabledSpecs ? queryDisabledSpecs() : []

  // 如果没有规格数据，返回默认规格结构
  if (!specsList || specsList.length === 0 || specsList[0].length === 0) {
    return {
      specsList: [],
      curSpecs: ['默认规格'], // 默认选中默认规格
      curDisabledSpecs: [],
    }
  }

  return {
    specsList: specsList,
    curSpecs: curSpecs,
    curDisabledSpecs: disabledSpecs,
  }
})

// 缓存的规格选项数据，在商品初始化时计算一次，避免重复计算
const cachedSpecOptions = ref([])

// 初始化规格选项数据，处理SKU列表并过滤可用选项
const initSpecOptions = () => {
  if (!spu.value || !spu.value.skuList) {
    cachedSpecOptions.value = []
    return
  }

  const disabledSpecs = queryDisabledSpecs ? queryDisabledSpecs() : []

  const options = spu.value.skuList.map(sku => {
    const isDisabled = checkSkuDisabled(sku, disabledSpecs)

    return {
      id: sku.skuId,
      name: getSkuDisplayName(sku),
      image: sku.listImageUrl,
      disabled: isDisabled,
      skuData: sku,
    }
  })

  // 只缓存可选择的SKU（过滤掉禁用的）
  cachedSpecOptions.value = options.filter(option => !option.disabled)

  console.log('初始化规格选项数据:', {
    totalOptions: options.length,
    selectableOptions: cachedSpecOptions.value.length
  })
}

// 判断商品是否有规格选择
const hasSpecs = computed(() => {
  const specs = specOptions.value
  return specs && specs.specsList && specs.specsList.length > 0 && specs.specsList[0].length > 0
})

// 显示用的规格列表，无规格时显示默认规格
const displaySpecsList = computed(() => {
  return hasSpecs.value ? specOptions.value.specsList : [['默认规格']]
})


// ===================== 工具函数 - 规格处理相关 =======================
// 检查SKU是否被禁用，通过对比SKU规格与禁用规格列表
const checkSkuDisabled = (sku, disabledSpecs) => {
  const skuSpecs = getSkuSpecs(sku)
  return skuSpecs.some(spec => disabledSpecs.includes(spec))
}

// 获取SKU的规格数组，提取param参数并格式化
const getSkuSpecs = (sku) => {
  const { param, param1, param2, param3 } = sku
  return ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3]
    .filter(p => p !== '_p0_undefined' && p !== '_p1_undefined' &&
      p !== '_p2_undefined' && p !== '_p3_undefined')
}

// ===================== 核心函数 - 商品详情加载 =======================
// 商品详情主加载函数，处理商品数据获取、初始化和各种状态设置
const loadGoodsDetail = async () => {
  // 根据业务代码判断是否显示物流服务
  isShowLogisticsServices.value = getBizCode() !== 'fupin'

  try {
    // 获取商品SPU数据
    const json = await querySpu()

    // 处理API响应错误
    if (json.code !== '0000') {
      if (json.code === '8888') {
        console.warn('此商品信息更新中，暂时无法购买，请您选购其他商品。')
      } else {
        console.warn(json.msg)
      }
      detailErr.value = true
      isLoading.value = false
      return
    }

    // 验证商品数据有效性
    if (!spu.value) {
      console.warn('商品数据为空')
      isLoading.value = false
      return
    }

    // 检查当前SKU并判断是否为京东商品
    const sku = querySku()
    if (sku && sku.supplierCode) {
      isJD.value = sku.supplierCode.indexOf(JD_GOODS_CODE) > -1
    }

    // URL更新：如果没有skuId参数但有默认SKU，更新URL保持状态同步
    if (!route.params.skuId && !hasUpdatedUrlForSku.value && sku && sku.skuId) {
      try {
        await router.replace({
          name: 'zq-cm-goods-detail',
          params: { goodsId, skuId: sku.skuId },
          query: route.query
        })
        hasUpdatedUrlForSku.value = true
      } catch (e) {
        console.warn('更新 URL skuId 失败:', e)
      }
    }

    // 初始化规格选项数据
    initSpecOptions()

    // 处理营销活动数据
    processMarketingTemplates()

    // 基础数据加载完成，更新页面状态
    isLoading.value = false
    isDataGet.value = true

    // 等待DOM更新完成
    await nextTick()

    // 添加内容加载动画效果
    setTimeout(() => {
      contentLoaded.value = true
      initialLoadComplete.value = true
    }, 100)

    // 京东商品异步加载额外信息
    if (isJD.value) {
      Promise.all([
        queryPredictSku(),
        checkIsSkuSale()
      ]).catch(error => {
        console.error('加载额外商品信息失败:', error)
      })
    }

    // 延迟滚动到选中规格位置
    setTimeout(() => {
      scrollToSelectedSpec()
    }, 200)

    // 根据起购要求设置初始购买数量
    const lowestBuy = lowestBuyObj.value
    if (lowestBuy.isLowestBuy) {
      goodsNum.value = lowestBuy.lowestBuyNum
    }

    // 初始化懒加载商品介绍组件
    setTimeout(() => {
      lazyGoodsIntroduceRef.value?.init()
    }, 500)

    // 处理默认规格选择
    await nextTick()
    if (!hasSpecs.value && specOptions.value.curSpecs.length === 0) {
      selectSpec('默认规格')
    }

    // 检查用户权限和限制
    await checkWhiteUserLimit()

    // 查询商品销售区域限制
    await querySale()

    // 已登录用户地址检查
    if (userStore.isLogin) {
      await addressCheck()
    }

    // 初始化分享功能
    await shareInit()

  } catch (error) {
    console.error('加载商品详情失败:', error)
    detailErr.value = true
    isLoading.value = false
  }
}

// ===================== 数据获取函数 - 商品介绍相关 =======================
// 获取商品介绍详情数据，用于懒加载显示
const getProductIntroduction = async () => {
  const sku = querySku()
  if (!sku) return

  const { supplierSkuId, supplierCode } = sku
  const [err, json] = await productIntroduction({
    supplierSkuId,
    supplierCode
  })
  if (!err) {
    // 直接更新响应式数据
    productIntroductionData.value = json
  }
}

// ===================== 数据获取函数 - 物流配送相关 =======================
// 查询物流配送时间，根据用户地址和商品信息预测送达时间
const queryPredictSku = async () => {
  const info = userStore.curAddressInfo
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName
  })

  const sku = currentSku.value
  if (!sku || !sku.supplierCode || !sku.supplierSkuId) {
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      predictContent: '预计48小时之内发货'
    }
    return
  }

  const params = {
    supplierCode: sku.supplierCode,
    supplierSkuId: sku.supplierSkuId,
    skuNum: goodsNum.value,
    addressInfoStr: addressInfo
  }

  const [err, res] = await queryPredictSkuPromise(params)
  if (!err) {
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      ...res
    }
    return
  }

  logisticsServicesInfo.value = {
    ...logisticsServicesInfo.value,
    predictContent: '预计48小时之内发货'
  }
}

// ===================== 数据获取函数 - SKU状态检查 =======================
// 检查SKU是否可售，验证商品当前销售状态
const checkIsSkuSale = async () => {
  const sku = currentSku.value
  if (!sku || !sku.supplierCode || !sku.supplierSkuId) return

  const params = {
    supplierCode: sku.supplierCode,
    supplierSkuId: sku.supplierSkuId
  }

  const [err, res] = await checkSkuSale(params)
  if (!err) {
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      ...res[0]
    }
    return
  }

  logisticsServicesInfo.value = {
    ...logisticsServicesInfo.value,
    logisticsType: 0,
    returnRuleStr: ''
  }
}

// ===================== 工具函数 - SKU显示相关 =======================
// 获取SKU的显示名称，将多个规格参数组合成可读的显示文本
const getSkuDisplayName = (sku) => {
  const specs = []
  if (sku.param) specs.push(sku.param)
  if (sku.param1) specs.push(sku.param1)
  if (sku.param2) specs.push(sku.param2)
  if (sku.param3) specs.push(sku.param3)
  return specs.join('，') || '默认'
}

// ===================== 数据获取函数 - 用户权限检查 =======================
// 检查白名单用户限制，验证用户是否有购买权限
const checkWhiteUserLimit = async () => {
  const goodsDetail = spu.value
  if (goodsDetail && goodsDetail.isCheckWhiteUser && goodsDetail.isCheckWhiteUser === '1') {
    // 如果登录的话，查询用户是否有资格在白名单内
    if (userStore.isLogin) {
      const [err, json] = await isWhiteUserLimitCheck(goodsId)
      if (!err) {
        limitState.value = json
      }
    }
  }
}

// ===================== 数据获取函数 - 区域销售限制 =======================
// 查询商品限制销售区域，检查当前地址是否允许购买该商品
const querySale = async () => {
  const info = userStore.curAddressInfo
  const params = {
    area: JSON.stringify({
      provinceId: info.provinceId,
      cityId: info.cityId,
      countyId: info.countyId,
      townId: info.townId
    }),
    goodsIdList: goodsId
  }

  // 商品数据正常时才进行区域限购查询
  if (!detailErr.value) {
    if (userStore.isLogin) {
      const [err, json] = await getLimitAreaList(params)
      if (!err && json) {
        regionalSalesState.value = json.length <= 0
      }
    }
    isDataGet.value = true
  } else {
    isDataGet.value = false
  }
}

// ===================== 工具函数 - 地址验证 =======================
// 地址检查方法，验证收货地址是否符合配送要求
const addressCheck = async () => {
  showLoadingToast()
  const [err, json] = await jdAddressCheck()
  closeToast()

  if (err) {
    showToast(err.msg)
    return false
  }

  if (!json) {
    $alert({
      title: '',
      message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存!',
      confirmButtonText: '修改地址',
      cancelButtonText: '取消',
      showCancelButton: true,
      onConfirmCallback: () => {
        // 确认修改地址
        router.push({
          name: 'address-edit',
          query: {
            addrId: userStore.curAddressInfo.addressId,
            isInvalid: '1'
          }
        })
      }
    })
    return false
  }

  return true
}

// ===================== 营销活动处理函数 =======================
// 处理营销活动模板，分类不同类型的营销位信息
const processMarketingTemplates = () => {
  const goodsDetail = spu.value
  if (!goodsDetail || !goodsDetail.marketTemplates) {
    return
  }

  // 活动营销位信息 （templateType 为 营销位类型）
  marketTemplatesType1.value = goodsDetail.marketTemplates.filter(
    (item) => item.templateType === '1'
  )
  marketTemplatesType4.value = goodsDetail.marketTemplates.filter(
    (item) => item.templateType === '4'
  )

  // 如果有营销位类型1，获取电子券信息
  if (userStore.isLogin && marketTemplatesType1.value.length > 0) {
    getElectronic()
  }
}

// 获取电子券信息，处理特定营销活动的优惠券数据
const getElectronic = async () => {
  if (userStore.isLogin && marketTemplatesType1.value && marketTemplatesType1.value.length > 0) {
    if (
      marketTemplatesType1.value?.[0] &&
      marketTemplatesType1.value[0].reqType === '1' &&
      marketTemplatesType1.value[0].templateNo === 'wxy618'
    ) {
      const [err, json] = await getActiveList({ templateNo: marketTemplatesType1.value[0].templateNo || '' })
      if (!err) {
        reducePrice.value = json
      }
    }
  }
}

// ===================== 工具函数 - 复制操作 =======================
// 复制商品编码到剪贴板，提供用户便捷的商品信息获取方式
const handleCopyCode = async () => {
  try {
    await toClipboard(currentSku.value.supplierSkuId)
    showToast('复制成功');
  } catch (error) {
    console.error('复制失败:', error)
    showToast('复制失败');
  }
}

// ===================== 工具函数 - 规格显示 =======================
// 获取规格组名称，根据规格数量动态生成显示名称
const getSpecGroupName = (groupIndex) => {
  return hasSpecs.value ? `规格${groupIndex + 1}` : '规格'
}

// ===================== 事件处理函数 - 规格选择 =======================
// 处理规格选择逻辑，支持字符串规格和对象规格两种形式
const selectSpec = (spec) => {
  console.log('选择规格:', spec)

  // 处理默认规格的特殊情况
  if (typeof spec === 'string') {
    const { curDisabledSpecs, curSpecs } = specOptions.value

    if (curDisabledSpecs.indexOf(spec) >= 0) return

    if (spec === '默认规格' && curSpecs.indexOf(spec) >= 0) {
      return
    }

    // 如果传入的是规格字符串，使用原有的setSpecs方法
    if (spec === '默认规格') {
      // 对于默认规格，不需要特殊处理，保持当前状态
      // 如果没有规格数据，curSpecs应该为空数组
      if (!querySpecsList() || querySpecsList().length === 0 || querySpecsList()[0].length === 0) {
        curSpecs.value = []
      }
    } else {
      setSpecs(spec)
    }

    // 切换规格时重置数量为1，但要考虑起购要求
    const lowestBuy = lowestBuyObj.value
    goodsNum.value = lowestBuy.isLowestBuy ? lowestBuy.lowestBuyNum : 1
  }

  // 如果传入的是完整的spec对象
  else if (spec && spec.skuData) {
    curSkuId.value = spec.skuData.skuId

    // 更新当前规格
    const { param, param1, param2, param3 } = spec.skuData
    curSpecs.value = ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3]
      .filter(p => p !== '_p0_undefined' && p !== '_p1_undefined' &&
        p !== '_p2_undefined' && p !== '_p3_undefined')

    // 切换规格时重置数量为1，但要考虑起购要求
    const lowestBuy = lowestBuyObj.value
    goodsNum.value = lowestBuy.isLowestBuy ? lowestBuy.lowestBuyNum : 1
  }

  // 触发相关数据更新和滚动
  nextTick(() => {
    debouncedUpdateGoodsInfo()
  })

  // 延迟执行滚动，确保DOM完全更新
  setTimeout(() => {
    scrollToSelectedSpec()
  }, 100)
}

// ===================== 工具函数 - 滚动控制 =======================
// 自动滚动到选中的规格，确保选中项在可视区域内居中显示
const scrollToSelectedSpec = async () => {
  console.log('开始执行 scrollToSelectedSpec')

  // 等待多个 tick 确保 DOM 完全更新
  await nextTick()
  await nextTick()

  // 通过子组件引用获取 specOptionsRef
  const container = specSelectionRef.value?.specOptionsRef
  if (!container) {
    console.log('specOptionsRef 不存在')
    return
  }

  // 修复选择器，使用正确的类名
  const selectedSpecElement = container.querySelector('.spec-option.is-active')
  console.log('找到的选中元素:', selectedSpecElement)

  if (!selectedSpecElement) {
    console.log('未找到选中的规格元素，尝试查找所有规格元素')
    const allSpecs = container.querySelectorAll('.spec-option')
    console.log('所有规格元素:', allSpecs)
    return
  }

  const containerWidth = container.clientWidth
  const selectedElementLeft = selectedSpecElement.offsetLeft
  const selectedElementWidth = selectedSpecElement.offsetWidth

  console.log('滚动计算参数:', {
    containerWidth,
    selectedElementLeft,
    selectedElementWidth,
    scrollWidth: container.scrollWidth
  })

  // 计算需要滚动的距离，让选中的规格居中显示
  const targetScrollLeft = selectedElementLeft - (containerWidth / 2) + (selectedElementWidth / 2)

  // 确保滚动位置不会超出边界
  const maxScrollLeft = container.scrollWidth - containerWidth
  const finalScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft))

  console.log('滚动目标位置:', {
    targetScrollLeft,
    maxScrollLeft,
    finalScrollLeft
  })

  // 使用 requestAnimationFrame 确保在正确的时机执行滚动
  requestAnimationFrame(() => {
    console.log('执行滚动到位置:', finalScrollLeft)
    container.scrollTo({
      left: finalScrollLeft,
      behavior: 'smooth'
    })
  })
}

// ===================== 事件处理函数 - 媒体轮播 =======================
// 处理图片预览操作，设置预览索引
const handleImagePreview = ({ item, index }) => {
  // 图片预览逻辑
  if (item.type === 'image') {
    previewImageIndex.value = index
    // 可以在这里实现图片预览功能
  }
}

// 处理轮播切换事件
const handleSlideChange = () => {
  // 轮播切换回调
  // console.log('当前轮播索引:', index)
}

// ===================== 事件处理函数 - 页面导航 =======================
// 返回商品列表页面
const handleGoToGoodsList = () => {
  router.push('/zq/manager/goods-list')
}

// ===================== 监听器 - 数据变化响应 =======================
// 监听地址变化，重新查询物流信息和购物车数据
const watchAddress = computed(() => userStore.curAddressInfo)
watch(watchAddress, async (newAddr, oldAddr) => {
  if (newAddr && oldAddr && (
    newAddr.provinceId !== oldAddr.provinceId ||
    newAddr.cityId !== oldAddr.cityId ||
    newAddr.countyId !== oldAddr.countyId ||
    newAddr.townId !== oldAddr.townId
  )) {
    if (isJD.value) {
      await queryPredictSku()
    }
    // 新增：地址切换时调用购物车query
    if (userStore.isLogin) {
      try {
        await newCartStore.query()
        console.log('地址切换后购物车数据已更新')
      } catch (error) {
        console.error('地址切换后更新购物车数据失败:', error)
      }
    }
  }
}, { deep: true })

// 监听规格数据变化，用于调试和状态跟踪
watch([curSpecs, () => queryDisabledSpecs()], ([newCurSpecs, newDisabledSpecs]) => {
  console.log('规格数据更新:', {
    curSpecs: newCurSpecs,
    disabledSpecs: newDisabledSpecs
  })
}, { deep: true })

// 监听当前SKU ID变化，自动滚动到选中规格
watch(curSkuId, (newSkuId, oldSkuId) => {
  if (newSkuId && newSkuId !== oldSkuId && initialLoadComplete.value) {
    console.log('SKU ID 变化，触发滚动:', { newSkuId, oldSkuId })
    setTimeout(() => {
      scrollToSelectedSpec()
    }, 150)
  }
})

// 监听登录状态变化，重新检查用户权限和营销活动
watch(() => userStore.isLogin, async (newLoginStatus, oldLoginStatus) => {
  if (newLoginStatus !== oldLoginStatus && spu.value) {
    // 登录状态发生变化，重新检查白名单用户限制
    await checkWhiteUserLimit()

    // 如果用户登录了，重新获取营销活动信息
    if (newLoginStatus && marketTemplatesType1.value.length > 0) {
      await getElectronic()
    }

    // 新增：登录状态变化时调用购物车query
    if (newLoginStatus) {
      try {
        await newCartStore.query()
        console.log('登录后购物车数据已更新')
      } catch (error) {
        console.error('登录后更新购物车数据失败:', error)
      }
    }
  }
})

// ===================== 生命周期钩子 =======================
// 组件挂载时初始化数据和事件监听
onMounted(async () => {
  loadGoodsDetail()
  // 监听滚动事件，实时保存滚动位置
  window.addEventListener('scroll', saveScrollPosition, { passive: true })

  // 新增：页面进入时判断登录状态，如果登录了就查询购物车
  if (userStore.isLogin) {
    try {
      await newCartStore.query()
      console.log('页面加载时购物车数据已更新')
    } catch (error) {
      console.error('页面加载时更新购物车数据失败:', error)
    }
  }
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  // 清理滚动事件监听器
  window.removeEventListener('scroll', saveScrollPosition)
})

// ===================== 分享功能处理 =======================
// 初始化分享数据，根据业务类型设置不同的分享文案
const shareInit = async () => {
  const bizCode = getBizCode()
  const intro = () => {
    const sku = spu.value?.skuList?.[0] || currentSku.value
    if (bizCode === 'ziying') {
      return sku.comment || '足不出户囤遍好物！购商品，来精选。'
    } else if (bizCode === 'fupin') {
      return sku.comment || '消费帮扶，共献爱心，乡村振兴，有我联通。'
    } else if (bizCode === 'fulihui') {
      return sku.comment || '足不出户囤遍好物！购商品，来福利汇。'
    } else if (bizCode === 'lnzx') {
      return sku.comment || '联农智选，好货甄选，品质可信。'
    } else {
      return sku.comment || sku.name || sku.merchantName || ''
    }
  }

  const firstSku = spu.value?.skuList?.[0] || currentSku.value
  shareData.title = firstSku.name || goodsInfo.value.name
  shareData.describe = intro()
  shareData.picUrl = goodsInfo.value.imageUrl || ''
  shareData.link = await getDefaultShareUrl()
  logger.log('shareInfo', shareData)
  setWeiXinShareData(shareData)
}
</script>

<style scoped lang="less">
.goods-detail {
  min-height: 100vh;
  background-color: #FFFFFF;
  padding-bottom: 55px;
}

.goods-content {
  background-color: #FFFFFF;
}

.image-section {
  background-color: #FFFFFF;
  padding: 0;
}

// 规格选择区域样式
.spec-section {
  padding: 10px 17px 0 17px;
  background-color: #FFFFFF;
  margin-bottom: 10px;

  .spec-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .spec-title {
      font-size: 16px;
      color: #171E24;
      font-weight: 500;
    }

    .goods-code {
      display: flex;
      align-items: center;
      gap: 4px;

      .code-label {
        font-size: 12px;
        color: #999999;
      }

      .code-value {
        font-size: 12px;
        color: #999999;
      }

      .copy-icon {
        width: 10px;
        height: 10px;
        flex-shrink: 0;
        margin-left: 2px;
        cursor: pointer;
      }
    }
  }

  .spec-options {
    .radio-wrapper {
      line-height: 40px;

      .spec-group-title {
        font-size: 14px;
        color: #171E24;
        font-weight: 500;
        margin-bottom: 8px;
        margin-top: 16px;

        &:first-child {
          margin-top: 0;
        }
      }

      button {
        display: inline-block;
        min-width: 75px;
        font-size: 13px;
        color: #171E24;
        line-height: 1.2;
        background: #F7F7F7;
        border-radius: 4px;
        padding: 8px 12px;
        margin-right: 8px;
        margin-bottom: 8px;
        outline: none;
        border: 1px solid transparent;
        cursor: pointer;
        transition: all 0.2s ease;

        &.active {
          background: rgba(255, 120, 10, 0.10);
          border: 1px solid var(--wo-biz-theme-color);
          color: var(--wo-biz-theme-color);
        }

        &.disabled {
          background: #F7F7F7;
          color: #B1BEC9;
          cursor: not-allowed;
        }
      }
    }
  }
}
</style>
